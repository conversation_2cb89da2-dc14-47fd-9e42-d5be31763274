{"name": "pars-vue", "version": "development", "private": true, "scripts": {"dev": "vite --host", "build": "vite build", "serve": "vite preview", "prettier": "prettier --write ."}, "dependencies": {"@codemirror/autocomplete": "^6.18.6", "@codemirror/basic-setup": "^0.20.0", "@codemirror/commands": "^6.8.1", "@codemirror/lang-javascript": "^6.2.4", "@codemirror/language": "^6.11.2", "@codemirror/search": "^6.5.11", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.3", "@codemirror/view": "^6.38.1", "@guolao/vue-monaco-editor": "^1.5.5", "@intlify/unplugin-vue-i18n": "6.0.8", "@vue-flow/background": "^1.3.2", "@vue-flow/controls": "^1.1.2", "@vue-flow/core": "^1.45.0", "@vue-flow/minimap": "^1.5.3", "@vue-flow/node-resizer": "^1.5.0", "axios": "^1.9.0", "codemirror": "^6.0.2", "dayjs": "^1.11.13", "echarts": "^5.6.0", "json-editor-vue": "^0.18.1", "md-editor-v3": "5.6.1", "monaco-editor": "^0.52.2", "pinia": "^3.0.3", "sanitize-html": "^2.17.0", "vanilla-jsoneditor": "^3.7.0", "vue": "^3.5.16", "vue-color-input": "^2.0.0", "vue-echarts": "^7.0.3", "vue-i18n": "11.1.5", "vue-json-pretty": "^2.4.0", "vue-router": "^4.5.1", "vue-tailwind-datepicker": "^1.7.3", "vuedraggable": "4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@vitejs/plugin-vue": "^5.2.4", "autoprefixer": "^10.4.21", "postcss": "^8.5.5", "prettier": "^3.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.7.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "6.3.5"}}