const routes = [
  {
    path: "documentation",
    name: "documentation",
    component: () => import('@/views/app/documentation/index.vue'),
    meta: {
      title: 'Documentation',
    }
  },
  {
    path: "documentation/nodes",
    name: "documentation-nodes",
    component: () => import('@/views/app/documentation/nodes.vue'),
    meta: {
      title: 'Node Documentation',
    }
  },
  {
    path: "documentation/what-is-pars",
    name: "documentation-what-is-pars",
    component: () => import('@/views/app/documentation/what-is-pars.vue'),
    meta: {
      title: 'What is Pars',
    }
  },
  {
    path: "documentation/integration",
    name: "documentation-integration",
    component: () => import('@/views/app/documentation/integration.vue'),
    meta: {
      title: 'Integration Guide',
    }
  }
]

export default routes;
