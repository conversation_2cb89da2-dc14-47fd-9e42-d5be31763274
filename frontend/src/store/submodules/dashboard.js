import { securedAxios } from '@/utils/axios';
import { defineStore } from 'pinia';

const path = '/dash'
export const useDashStore = defineStore('dash', {
    actions: {
        async Get() {
            try {
                const { data } = await securedAxios.get(`${path}`)
                return data
            } catch (error) {
                throw error
            }
        },
        async GetCurrentAdmin() {
            try {
                const { data } = await securedAxios.get(`${path}/admin`)
                return data
            } catch (error) {
                throw error
            }
        }
    },
    getters: {
    }
})