import { useAuthStore } from "@/store/submodules/auth";

export function MenuWithGroupList() {
  let customMenu = [
    {
      "name": "Organization",
      "childrens": [
        {
          "title": "Organization Management",
          "childs": [
            {
              "name": "organization",
              "route": "organization-list"
            },
            {
              "name": "admin",
              "route": "admin-list"
            },
            {
              "name": "access-token",
              "route": "access-token-list"
            },
          ]
        },
      ]
    },
    {
      "name": "Simulation",
      "childrens": [
        {
          "title": "Simulation",
          "childs": [
            {
              "name": "simulate",
              "route": "simulate-list"
            },
            {
              "name": "workspace",
              "route": "workspace"
            },
            // {
            //   "name": "default request list",
            //   "route": "default-request-list"
            // },
          ]
        },
      ]
    },
    {
      "name": "Others",
      "childrens": [
        {
          "title": "Others",
          "childs": [
            {
              "name": "documentation",
              "route": "documentation"
            }
          ]
        },
      ]
    },
  ];

  const store = useAuthStore();
  const user = computed(() => store.getCurrentUser);
  const permissions = user.value?.role?.permissions;

  // sort menu by name
  customMenu.map((item) => {
    if (item.childrens) {
      item.childrens.map((child) => {
        // first sort by title
        child.childs.sort((a, b) => {
          if (a.name < b.name) {
            return -1;
          }
          if (a.name > b.name) {
            return 1;
          }
        });
      });
    }
  });


  customMenu.map((item) => {
    if (item.childrens) {
      item.childrens.map((child) => {
        child.childs.map((subchild) => {
          if (subchild.skip) {
            return;
          }
          let permission = permissions?.find((permission) => permission.entity === subchild.name);
          if (permission) {
            if (!permission.read) {
              child.childs = child.childs.filter((subchild) => subchild.name !== permission.entity);
            }
          }
        });
      });
    }

    if (item.childrens.length === 0) {
      customMenu = customMenu.filter((ritem) => ritem.name !== item.name);
    }
  });

  return customMenu;
}