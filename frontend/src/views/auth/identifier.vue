<template>
  <div class="rounded-md bg-rose-500 dark:bg-rose-900/50 p-4" v-if="sessiondrop">
    <div class="flex items-center">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-rose-400 dark:text-rose-700" viewBox="0 0 20 20" fill="currentColor"
          aria-hidden="true">
          <path fill-rule="evenodd"
            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z"
            clip-rule="evenodd" />
        </svg>
      </div>
      <div class="ml-3 flex-1 md:flex md:justify-between">
        <p class="text-sm text-rose-50">
          {{ $t('auth.session_drop') }}
        </p>
      </div>
    </div>
  </div>

  <div>
    <h1 class="text-xl sm:text-2xl font-semibold text-gray-800 dark:text-neutral-200">
      {{ $t('auth.identifier.title') }}
    </h1>
    <p class="mt-1 text-sm text-gray-500 dark:text-neutral-500">
      {{ $t('auth.identifier.description') }}
    </p>
  </div>


  <form @submit.prevent="Identify()">
    <div class="space-y-5">
      <div>
        <label for="email" class="block mb-2 text-sm font-medium text-gray-800 dark:text-white">
          Email
        </label>

        <input type="email" id="email" v-model="form.email" required class="t-input" placeholder="<EMAIL>">
      </div>

      <button type="submit"
        style="background-color: #8e6f4d; border-color: #8e6f4d;" class="py-2.5 px-3 w-full inline-flex justify-center items-center gap-x-2 text-sm font-semibold rounded-lg border text-white hover:opacity-90 disabled:opacity-50 disabled:pointer-events-none dark:focus:outline-none dark:focus:ring-1 dark:focus:ring-neutral-600">
        {{ $t('general.next') }}
      </button>
    </div>
  </form>

  <MonoSlide v-model="slideOver" :message="$t('auth.private_browsing')" />
</template>

<script setup>
import { useAuthStore } from '@/store/submodules/auth';

const store = useAuthStore()
const processing = ref(false)
const slideOver = ref(false)
const route = useRoute()
const router = useRouter()
const toast = inject("toast")

const { t } = useI18n()
const form = reactive({
  email: null,
})

const sessiondrop = ref(false)
if (route.query.sessionDrop) {
  sessiondrop.value = true
  toast.info(t('auth.session_drop'))
}

function Identify() {
  if (!form.email) {
    toast.error(t('auth.please_enter_email'))
    return
  }
  processing.value = true

  store.Identify(form).then((res) => {

    let query = {
      identiy: window.btoa(form.email),
    }
    if (res.data.tfa_forced) {
      query.tfa_forced = res.data.tfa_forced
    }
    if (res.data.tfa_img) {
      query.tfa_img = res.data.tfa_img
    }
    if (res.data.tfa_enabled) {
      query.tfa_enabled = res.data.tfa_enabled
    }


    router.push({
      name: 'auth-password', query: query
    })

  }).catch((err) => {
    console.log(err)
  }).finally(() => {
    processing.value = false
  })
}

</script>