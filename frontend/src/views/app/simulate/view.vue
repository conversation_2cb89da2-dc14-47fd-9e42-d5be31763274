<template>
  <div v-if="loading" class="max-w-7xl mx-auto flex justify-center items-center py-10">
    <MonoIcon class="size-20 animate-spin" name="spin" />
  </div>

  <div class="max-w-7xl mx-auto" v-else>
    <div
      class="p-5 pb-0 bg-white border border-gray-200 shadow-sm rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
      <div class="vue-flow-container" style="width: 100%; height: 300px;">
        <VueFlow 
          :min-zoom="0.2" 
          :max-zoom="4" 
          :nodes="nodes" 
          :edges="edges" 
          :node-types="nodeTypes"
          :nodes-draggable="false" 
          :edges-updatable="false" 
          :fit-view="true" 
          :pan-on-drag="false" 
          :pan-on-scroll="false"
          :zoom-on-scroll="false"
          :zoom-on-pinch="false"
          :zoom-on-double-click="false"
        >
          <Background :size="1" :gap="25" pattern-color="#ddd" variant="dots" />
        </VueFlow>
      </div>

      <div class="mt-4 px-4">
        <h1 class="text-2xl font-semibold text-gray-800 dark:text-white">
          {{ simulate.name }}
        </h1>
      </div>

      <div
        class="py-2 flex flex-row justify-between items-center gap-x-2 whitespace-nowrap overflow-x-auto overflow-y-hidden [&amp;::-webkit-scrollbar]:h-2 [&amp;::-webkit-scrollbar-thumb]:rounded-full [&amp;::-webkit-scrollbar-track]:bg-gray-100 [&amp;::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&amp;::-webkit-scrollbar-track]:bg-neutral-700 dark:[&amp;::-webkit-scrollbar-thumb]:bg-neutral-500">
        <t-profile-tabs @update:modelValue="(id) => setCurrentTab(id, true)" :tabs="tabs.filter((tab) => !tab.hide)"
          v-model="currentTabId" />

        <div class="">
          <router-link :to="{ name: 'workspace-edit', params: { id: route.params.id } }"
            class="px-2.5 py-1.5 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
            <MonoIcon class="mr-2 size-2" name="arrow-right" />
            {{ t("simulates.go_to_simulate") }}
          </router-link>
        </div>
      </div>
    </div>

    <div class="mt-5">
      <component :is="currentTab.component" :key="currentTab.id" :detail="simulate" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { t } from "@/plugins/i18n";
import { useSimulateStore } from '@/store/submodules/simulate';
import { defineAsyncComponent } from "vue";
import { VueFlow, useVueFlow } from '@vue-flow/core'
import { Background } from '@vue-flow/background'


import StartNode from '../../../components/flow-editor/nodes/start-node.vue'
import EndNode from '../../../components/flow-editor/nodes/end-node.vue'
import SwitchNode from '../../../components/flow-editor/nodes/switch-node.vue'
import TimeoutNode from '../../../components/flow-editor/nodes/timeout-node.vue'
import FunctionNode from '../../../components/flow-editor/nodes/function-node.vue'
import AiAgentNode from '../../../components/flow-editor/nodes/ai-agent-node.vue'
import ExcelNode from '../../../components/flow-editor/nodes/excel-node.vue'

import '@vue-flow/core/dist/style.css'
import '@vue-flow/core/dist/theme-default.css'
import '@vue-flow/node-resizer/dist/style.css'
import '@vue-flow/controls/dist/style.css'

import '../../../components/flow-editor/styles/editor.css'

const nodeTypes = {
  startNode: StartNode,
  endNode: EndNode,
  switchNode: SwitchNode,
  timeoutNode: TimeoutNode,
  functionNode: FunctionNode,
  aiAgentNode: AiAgentNode,
  excelNode: ExcelNode,
}

const simulateStore = useSimulateStore();
const route = useRoute();
const router = useRouter();
const loading = ref(true);

const { fitView } = useVueFlow();

const nodes = ref([])
const edges = ref([])
const simulate = ref(null);

const tabs = [
  {
    id: "overview",
    component: defineAsyncComponent(() => import("@/components/simulates/simulate-overview.vue")),
    title: t("simulates.tabs.overview"),
    icon: "workflow",
    isActive: false,
    disabled: false,
  },
  {
    id: "logs",
    component: defineAsyncComponent(() => import("@/components/simulates/simulate-logs.vue")),
    title: t("simulates.tabs.logs"),
    icon: "terminal",
    isActive: false,
    disabled: false,
  },
  {
    id: "statistics",
    component: defineAsyncComponent(() => import("@/components/simulates/simulate-statistics.vue")),
    title: t("simulates.tabs.statistics"),
    icon: "database",
    isActive: false,
    disabled: false,
  },
  {
    id: "versions",
    component: defineAsyncComponent(() => import("@/components/simulates/simulate-versions.vue")),
    title: t("simulates.tabs.versions"),
    icon: "filter",
    isActive: false,
    disabled: false,
  },
  {
    id: "default-request",
    component: defineAsyncComponent(() => import("@/components/simulates/simulate-default-request.vue")),
    title: t("simulates.tabs.default-request"),
    icon: "code",
    isActive: false,
    disabled: false,
  },
  {
    id: "retry-management",
    component: defineAsyncComponent(() => import("@/components/simulates/simulate-retry-management.vue")),
    title: t("simulates.tabs.retry-management"),
    icon: "refresh",
    isActive: false,
    disabled: false,
  },
  {
    id: "scheduled-works",
    component: defineAsyncComponent(() => import("@/components/simulates/simulate-scheduled-works.vue")),
    title: t("simulates.tabs.scheduled-works"),
    icon: "calendar",
    isActive: false,
    disabled: false,
  },
];

const setCurrentTab = (tab, withId = false) => {
  router.push({
    query: {
      tab: tab,
    },
    replace: true,
  });

  if (withId) {
    let selectedTab = tabs.find((tabObject) => tabObject.id == tab);
    selectedTab ? (currentTab.value = selectedTab) : "";
    currentTabId.value = tab;
  } else {
    currentTab.value = tab;
    currentTabId.value = tab.id;
  }
};

const currentTabId = ref(null);
const currentTab = ref(null);

onMounted(async () => {
  setCurrentTab(route.query.tab ? route.query.tab : tabs[0].id, true);

  if (route.params.id) {
    try {
      const res = await simulateStore.GetSimulate(route.params.id)
      simulate.value = res;

      let loadedNodes = [];
      let loadedEdges = [];

      loadedNodes = res.nodes || [];
      loadedEdges = res.edges || [];

      const processedNodes = loadedNodes.map((node, index) => {

        // -----> for start node
        if (node.type === 'inputNode') {
          return {
            id: node.id,
            type: 'startNode',
            position: {
              x: node.position.x,
              y: node.position.y
            },
            data: {
              label: node.name,
              description: ""
            }
          };
        }

        // -----> for end node
        if (node.type === 'outputNode') {
          return {
            id: node.id,
            type: 'endNode',
            position: {
              x: node.position.x,
              y: node.position.y
            },
            data: {
              label: node.name,
              description: ""
            }
          };
        }

        // -----> for timeout node
        if (node.type === 'customNode' && node.content.kind === 'timeoutNode') {
          const timeout = node.content.config.time || 1;
          return {
            id: node.id,
            type: 'timeoutNode',
            position: {
              x: node.position.x,
              y: node.position.y
            },
            data: {
              label: node.name,
              description: "",
              timeout: timeout
            }
          };
        }

        // -----> for ai agent node
        if (node.type === 'customNode' && node.content.kind === 'aiAgentNode') {
          const selectedAgent = node.content.config.agentId || '';
          return {
            id: node.id,
            type: 'aiAgentNode',
            position: {
              x: node.position.x,
              y: node.position.y
            },
            data: {
              label: node.name,
              description: "",
              selectedAgent: selectedAgent
            }
          };
        }

        // -----> for excel node
        if (node.type === 'customNode' && node.content.kind === 'excelNode') {
          return {
            id: node.id,
            type: 'excelNode',
            position: {
              x: node.position.x,
              y: node.position.y
            },
            data: {
              label: node.name,
              description: "",
              fileName: node.content.config.fileName,
              excelColumns: node.content.config.excelColumns,
              selectedColumns: node.content.config.selectedColumns,
              excelData: node.content.config.excelData,
              fileType: node.content.config.fileType,
              operation: node.content.config.operation || 'get_data',
              dataLimit: node.content.config.dataLimit || 100,
              saveData: node.content.config.saveData || false
            }
          };
        }
        

        // -----> for switch node
        if (node.type === 'switchNode') {
          const statements = node.content.statements || [];
          const mode = node.content.hitPolicy || 'first';
          const mappedConditions = statements.map(statement => ({
            id: statement.id,
            expression: statement.condition,
            isDefault: statement.isDefault || false
          }));

          const result = {
            id: node.id,
            type: 'switchNode',
            position: {
              x: node.position.x,
              y: node.position.y
            },
            data: {
              label: node.name,
              description: "",
              conditions: mappedConditions,
              mode: mode
            }
          }

          return result;
        }

        const processedNode = {
          id: node.id,
          type: node.type,
          position: {
            x: node.position.x,
            y: node.position.y
          },
          data: {
            label: node.name,
            description: ""
          }
        };
        return processedNode;
      });

      const processedEdges = loadedEdges.map((edge, index) => {
        const processedEdge = {
          id: edge.id,
          source: edge.sourceId || edge.source,
          target: edge.targetId || edge.target,
          sourceHandle: edge.sourceHandle || null,
          targetHandle: edge.targetHandle || null,
          type: 'edge',
          animated: true,
          style: {
            stroke: '#ff0072',
            strokeWidth: 2,
            transition: 'stroke-dasharray 1s ease-in-out',
          },
          markerEnd: {
            type: 'arrowclosed',
          },
        };
        return processedEdge;
      });

      nodes.value = [...processedNodes];
      edges.value = [...processedEdges];

      setTimeout(() => {
        fitView({
          padding: 0.1,
          includeHiddenNodes: false,
          minZoom: 0.2,
          maxZoom: 1.5
        });
      }, 100);
    } catch (err) {
      console.error('API Error:', err);
    }
  } else {
    console.log('No route.params.id found');
  }

  loading.value = false;
});
</script>

<style scoped>
.vue-flow-container {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.vue-flow-container :deep(.vue-flow__background) {
  opacity: 1 !important;
}

.vue-flow-container :deep(.vue-flow__node) {
  cursor: pointer;
}

.vue-flow-container :deep(.vue-flow__edge) {
  cursor: pointer;
}
</style>