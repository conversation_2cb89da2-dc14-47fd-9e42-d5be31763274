<template>
  <div class="mx-auto max-w-9xl">
    <!-- Header -->
    <div class="mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          {{ $t('dashboard.welcome') }} {{ admin.name }}
        </h1>
        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
          {{ $t('dashboard.track_organization_performance') }}
        </p>
      </div>
    </div>

    <!-- Dashboard Statistics Cards -->
    <div class="space-y-6">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">

        <!-- Total Simulates -->
        <div
          class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border border-blue-200 dark:border-blue-700/30 rounded-xl p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-blue-600 dark:text-blue-400">
                {{ $t('dashboard.total_simulates') }}
              </p>
              <p class="text-3xl font-bold text-blue-900 dark:text-blue-100 mt-2">
                {{ dashboard?.total_simulate_count || 0 }}
              </p>
            </div>
            <div class="p-3 bg-blue-500/20 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                stroke="currentColor" class="w-6 h-6 text-blue-600 dark:text-blue-400">
                <path stroke-linecap="round" stroke-linejoin="round"
                  d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z" />
              </svg>
            </div>
          </div>
        </div>

        <!-- Total Evaluations -->
        <div
          class="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 border border-purple-200 dark:border-purple-700/30 rounded-xl p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-purple-600 dark:text-purple-400">
                {{ $t('dashboard.total_evaluations') }}
              </p>
              <p class="text-3xl font-bold text-purple-900 dark:text-purple-100 mt-2">
                {{ dashboard?.total_evaluation_count || 0 }}
              </p>
            </div>
            <div class="p-3 bg-purple-500/20 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                stroke="currentColor" class="w-6 h-6 text-purple-600 dark:text-purple-400">
                <path stroke-linecap="round" stroke-linejoin="round"
                  d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
              </svg>
            </div>
          </div>
        </div>

        <!-- Total Success -->
        <div
          class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border border-green-200 dark:border-green-700/30 rounded-xl p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-green-600 dark:text-green-400">
                {{ $t('dashboard.total_success') }}
              </p>
              <p class="text-3xl font-bold text-green-900 dark:text-green-100 mt-2">
                {{ dashboard?.total_success_count || 0 }}
              </p>
            </div>
            <div class="p-3 bg-green-500/20 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                stroke="currentColor" class="w-6 h-6 text-green-600 dark:text-green-400">
                <path stroke-linecap="round" stroke-linejoin="round"
                  d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
              </svg>
            </div>
          </div>
        </div>

        <!-- Total Errors -->
        <div
          class="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 border border-red-200 dark:border-red-700/30 rounded-xl p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-red-600 dark:text-red-400">
                {{ $t('dashboard.total_error') }}
              </p>
              <p class="text-3xl font-bold text-red-900 dark:text-red-100 mt-2">
                {{ dashboard?.total_error_count || 0 }}
              </p>
            </div>
            <div class="p-3 bg-red-500/20 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                stroke="currentColor" class="w-6 h-6 text-red-600 dark:text-red-400">
                <path stroke-linecap="round" stroke-linejoin="round"
                  d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
              </svg>
            </div>
          </div>
        </div>

      </div>

      <div class="mt-8">
        <div class="rounded-xl p-4 border border-amber-200 dark:border-amber-700/30" style="background: linear-gradient(to bottom, #e4bc72, #8e6f4d);">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mr-3">
                <img src="@/assets/images/pars.png" alt="PARS Logo" class="w-full h-full object-contain">
              </div>
              <div>
                <div class="flex items-center gap-2 mb-1">
                  <h3 class="text-2xl font-bold text-gray-900">PARS</h3>
                  <span class="px-2 py-1 bg-purple-600 text-white text-xs font-medium rounded-full">BETA</span>
                </div>
                <p class="text-xs text-gray-800">
                  <strong>P</strong>rocess, <strong>A</strong>utomation, <strong>R</strong>ule, <strong>S</strong>ervice
                </p>
              </div>
            </div>
            <a
              href="https://pars.guru/"
              target="_blank"
              rel="noopener noreferrer"
              class="inline-flex items-center gap-2 px-3 py-2 bg-white/20 hover:bg-white/30 text-gray-900 text-sm font-medium rounded-lg transition-colors backdrop-blur-sm border border-white/30"
            >
              {{ $t('dashboard.visit_website') }}
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 6H5.25A2.25 2.25 0 0 0 3 8.25v10.5A2.25 2.25 0 0 0 5.25 21h10.5A2.25 2.25 0 0 0 18 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25" />
              </svg>
            </a>
          </div>

          <p class="text-gray-800 mb-4 text-sm leading-relaxed">
            {{ $t('dashboard.pars_description') }}
          </p>

          <div class="grid grid-cols-2 lg:grid-cols-4 gap-3">
            <div class="bg-white/20 backdrop-blur-sm border border-white/30 rounded-lg p-3 hover:bg-white/30 transition-all">
              <div class="flex items-center mb-2">
                <div class="w-6 h-6 bg-blue-500/30 rounded-md flex items-center justify-center mr-2">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-3 h-3 text-blue-600">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.*************.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a6.759 6.759 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z" />
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                  </svg>
                </div>
                <h4 class="font-semibold text-gray-900 text-xs">Process</h4>
              </div>
              <p class="text-xs text-gray-700 leading-tight">{{ $t('dashboard.process_description') }}</p>
            </div>

            <div class="bg-white/20 backdrop-blur-sm border border-white/30 rounded-lg p-3 hover:bg-white/30 transition-all">
              <div class="flex items-center mb-2">
                <div class="w-6 h-6 bg-green-500/30 rounded-md flex items-center justify-center mr-2">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-3 h-3 text-green-600">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495" />
                  </svg>
                </div>
                <h4 class="font-semibold text-gray-900 text-xs">Automation</h4>
              </div>
              <p class="text-xs text-gray-700 leading-tight">{{ $t('dashboard.automation_description') }}</p>
            </div>

            <div class="bg-white/20 backdrop-blur-sm border border-white/30 rounded-lg p-3 hover:bg-white/30 transition-all">
              <div class="flex items-center mb-2">
                <div class="w-6 h-6 bg-purple-500/30 rounded-md flex items-center justify-center mr-2">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-3 h-3 text-purple-600">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z" />
                  </svg>
                </div>
                <h4 class="font-semibold text-gray-900 text-xs">Rule</h4>
              </div>
              <p class="text-xs text-gray-700 leading-tight">{{ $t('dashboard.rule_description') }}</p>
            </div>

            <div class="bg-white/20 backdrop-blur-sm border border-white/30 rounded-lg p-3 hover:bg-white/30 transition-all">
              <div class="flex items-center mb-2">
                <div class="w-6 h-6 bg-indigo-500/30 rounded-md flex items-center justify-center mr-2">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-3 h-3 text-indigo-600">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-16.5 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z" />
                  </svg>
                </div>
                <h4 class="font-semibold text-gray-900 text-xs">Service</h4>
              </div>
              <p class="text-xs text-gray-700 leading-tight">{{ $t('dashboard.service_description') }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Documentation Section -->
      <div class="mt-8">
        <div class="bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-900/20 dark:to-indigo-800/20 border border-indigo-200 dark:border-indigo-700/30 rounded-xl p-6">
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-indigo-500/20 rounded-lg">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                  stroke="currentColor" class="w-6 h-6 text-indigo-600 dark:text-indigo-400">
                  <path stroke-linecap="round" stroke-linejoin="round"
                    d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25" />
                </svg>
              </div>
              <h3 class="text-lg font-semibold text-indigo-900 dark:text-indigo-100">
                {{ $t('dashboard.documentation') }}
              </h3>
            </div>
            <button @click="goToAllDocuments" class="inline-flex items-center px-4 py-2 border border-indigo-300 dark:border-indigo-600 shadow-sm text-sm leading-4 font-medium rounded-md text-indigo-700 dark:text-indigo-200 bg-white dark:bg-indigo-800/50 hover:bg-indigo-50 dark:hover:bg-indigo-700/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3" />
              </svg>
              {{ $t('dashboard.view_all_documents') }}
            </button>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <!-- Article 1 -->
            <div class="bg-white dark:bg-indigo-900/30 rounded-lg p-4 border border-indigo-100 dark:border-indigo-700/50 hover:shadow-md transition-shadow cursor-pointer" @click="openArticle('what-is-pars')">
              <div class="flex items-start space-x-3">
                <div class="p-2 bg-indigo-100 dark:bg-indigo-800/50 rounded-md">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 text-indigo-600 dark:text-indigo-400">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z" />
                  </svg>
                </div>
                <div class="flex-1">
                  <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">
                    What is Pars
                  </h4>
                  <p class="text-xs text-gray-600 dark:text-gray-400">
                    Comprehensive information about Pars platform and its core features
                  </p>
                </div>
              </div>
            </div>

            <div class="bg-white dark:bg-indigo-900/30 rounded-lg p-4 border border-indigo-100 dark:border-indigo-700/50 hover:shadow-md transition-shadow cursor-pointer" @click="openArticle('service-integration')">
              <div class="flex items-start space-x-3">
                <div class="p-2 bg-indigo-100 dark:bg-indigo-800/50 rounded-md">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 text-indigo-600 dark:text-indigo-400">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244" />
                  </svg>
                </div>
                <div class="flex-1">
                  <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">
                    Integration Guide
                  </h4>
                  <p class="text-xs text-gray-600 dark:text-gray-400">
                    Integration guide for other services and systems
                  </p>
                </div>
              </div>
            </div>

            <div class="bg-white dark:bg-indigo-900/30 rounded-lg p-4 border border-indigo-100 dark:border-indigo-700/50 hover:shadow-md transition-shadow cursor-pointer" @click="openArticle('node-documentation')">
              <div class="flex items-start space-x-3">
                <div class="p-2 bg-indigo-100 dark:bg-indigo-800/50 rounded-md">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 text-indigo-600 dark:text-indigo-400">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z" />
                  </svg>
                </div>
                <div class="flex-1">
                  <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">
                    Node Documentation
                  </h4>
                  <p class="text-xs text-gray-600 dark:text-gray-400">
                    All node types and features available in the flow editor
                  </p>
                </div>
              </div>
            </div>


          </div>
        </div>
      </div>

      <div v-if="dashboard?.last_simulate" class="mt-8">
        <div
          class="bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900/20 dark:to-slate-800/20 border border-slate-200 dark:border-slate-700/30 rounded-xl p-6">
          <div class="flex items-start justify-between">
            <div class="flex items-start space-x-4">
              <div class="p-3 bg-slate-500/20 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                  stroke="currentColor" class="w-6 h-6 text-slate-600 dark:text-slate-400">
                  <path stroke-linecap="round" stroke-linejoin="round"
                    d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                </svg>
              </div>
              <div class="flex-1">
                <h3 class="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-2">
                  {{ $t('dashboard.last_simulate') }}
                </h3>
                <div class="space-y-2">
                  <div class="flex items-center space-x-2">
                    <span class="text-sm font-medium text-slate-600 dark:text-slate-400">{{ $t('simulates.name')
                      }}:</span>
                    <span class="text-sm text-slate-900 dark:text-slate-100 font-medium">
                      {{ dashboard.last_simulate.name || 'Untitled' }}
                    </span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span class="text-sm font-medium text-slate-600 dark:text-slate-400">{{ $t('simulates.version_name')
                      }}:</span>
                    <span
                      class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-slate-100 text-slate-800 dark:bg-slate-700 dark:text-slate-200">
                      {{ dashboard.last_simulate.version || 'v1.0.0' }}
                    </span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span class="text-sm font-medium text-slate-600 dark:text-slate-400">{{ $t('simulates.created_at')
                      }}:</span>
                    <span class="text-sm text-slate-700 dark:text-slate-300">
                      {{ formatDate(dashboard.last_simulate.created_at) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <button @click="viewSimulation(dashboard.last_simulate.ID)"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                  stroke="currentColor" class="w-4 h-4 mr-1">
                  <path stroke-linecap="round" stroke-linejoin="round"
                    d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z" />
                  <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                </svg>
                {{ $t('dashboard.view_simulation') }}
              </button>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useDashStore } from '@/store/submodules/dashboard';

const store = useDashStore();
const router = useRouter();
const dashboard = ref(null);
const admin = ref({});

const error = ref(null);

function formatDate(dateString) {
  if (!dateString) return 'N/A';
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    return 'Invalid Date';
  }
}

function viewSimulation(simulationId) {
  if (simulationId) {
    router.push({ name: 'simulate-view', params: { id: simulationId } });
  }
}

function goToAllDocuments() {
  // TODO: Navigate to documentation page
  router.push({ name: 'documentation' });
}

function openArticle(articleId) {
  if  (articleId === 'service-integration') {
    router.push({ name: 'documentation-integration' });
  } else if (articleId === 'node-documentation') {
    router.push({ name: 'documentation-nodes' });
  } else if (articleId === 'what-is-pars') {
    router.push({ name: 'documentation-what-is-pars' });
  } else   {
    router.push({ name: 'documentation', params: { article: articleId } });
  }
}

onMounted(async () => {
  try {
    const adminRes = await store.GetCurrentAdmin();
    admin.value = adminRes;

    const dashboardRes = await store.Get();
    dashboard.value = dashboardRes;

  } catch (err) {
    console.error('API Error:', err);
    error.value = {
      title: 'Error loading data',
      message: err.message || 'Something went wrong'
    };
  }
});

</script>
