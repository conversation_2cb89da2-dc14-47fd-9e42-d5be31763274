<template>
    <div
    class="p-3 flex flex-col bg-white border border-gray-200 shadow-sm rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
    <div class="p-3 space-y-5">
      <div class="divide-y divide-gray-200 dark:divide-gray-700">
        <div class="px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <span class="text-sm">{{ $t('profile.profile.email') }}</span>
          <span class="mt-1 text-sm sm:mt-0 sm:col-span-2">{{ adminData.email }}</span>
        </div>
        <div class="px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <span class="text-sm">{{ $t('profile.profile.first_name') }}</span>
          <span class="mt-1 text-sm sm:mt-0 sm:col-span-2">{{  adminData.name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useDashStore } from "@/store/submodules/dashboard";

const dashStore = useDashStore();
const loading = ref(true);
const adminData = ref({
  name: '',
  email: ''
});

onMounted(async () => {
  try {
    const response = await dashStore.GetCurrentAdmin();
    adminData.value = {
      name: response.name || '',
      email: response.email || ''
    };
  } catch (error) {
    console.error('Error fetching admin data:', error);
  } finally {
    loading.value = false;
  }
});
</script>