<template>
  <div class="max-w-7xl mx-auto">
    <div class="p-2 sm:p-5 sm:py-0 md:pt-5 space-y-5">
      <div
        class="p-5 pb-0 bg-white border border-gray-200 shadow-sm rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
        <figure>
          <svg class="w-full rounded-lg" preserveAspectRatio="none" width="1113" height="161" viewBox="0 0 1113 161"
            fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_666_220723)">
              <rect x="1" width="1112" height="348" fill="#818cf8"></rect>
              <path d="M512.694 359.31C547.444 172.086 469.835 34.2204 426.688 -11.3096H1144.27V359.31H512.694Z"
                fill="#6366f1"></path>
              <path
                d="M818.885 185.745C703.515 143.985 709.036 24.7949 726.218 -29.5801H1118.31V331.905C1024.49 260.565 963.098 237.945 818.885 185.745Z"
                fill="#4f46e5"></path>
            </g>
          </svg>
        </figure>

        <div class="-mt-24">
          <div
            class="relative flex w-[120px] h-[120px] mx-auto border-4 border-white rounded-full dark:border-neutral-800">
            <div class="rounded-full flex justify-center bg-indigo-500 items-center size-full">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
                stroke="currentColor" class="size-20 text-white">
                <path stroke-linecap="round" stroke-linejoin="round"
                  d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" />
              </svg>
            </div>
          </div>
          <div class="mt-3 text-center">
            <h1 class="text-xl capitalize font-semibold text-gray-800 dark:text-neutral-200">
              {{ CurrentUser.first_name }} {{ CurrentUser.last_name }}
            </h1>
            <p class="text-gray-500 dark:text-neutral-500">
              {{ CurrentUser.email }}
            </p>
          </div>
        </div>
        <div
          class="py-2 flex flex-row justify-between items-center gap-x-2 whitespace-nowrap overflow-x-auto overflow-y-hidden">
          <t-profile-nav />
        </div>
      </div>
      <router-view />
    </div>
  </div>
</template>

<script setup>
import { useAuthStore } from '@/store/submodules/auth';

const store = useAuthStore()

const CurrentUser = computed(() => store.getCurrentUser);
</script>