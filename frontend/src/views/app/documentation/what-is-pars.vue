<template>
  <component :is="currentLanguageComponent" />
</template>

<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import WhatIsParsEn from './what-is-pars-en.vue'
import WhatIsParsTr from './what-is-pars-tr.vue'

const { locale } = useI18n()

const currentLanguageComponent = computed(() => {
  return locale.value === 'tr' ? WhatIsParsTr : WhatIsParsEn
})
</script>

<style scoped>
</style>
