<template>
  <div class="what-is-pars">
    <div class="header">
      <div class="header-content">
        <router-link :to="{ name: 'documentation' }" class="back-link">
          ← {{ $t('routes.documentation') }}
        </router-link>
        <h1>{{ $t('routes.documentation-what-is-pars') }}</h1>
        <p>Comprehensive information about Pars platform and its core features</p>
      </div>
    </div>

    <div class="content">
      <div class="content-wrapper">

        <section class="section">
          <h2>What is Pars?</h2>
          <div class="pars-intro">
            <p class="pars-definition">
              <strong>PARS</strong> (Process, Automation, Rule, Service) is a powerful platform that enables you to
              automate your business processes and workflows — without limitations.
              PARS offers different node types and advanced features to help you design and manage even the most complex
              workflows.
            </p>
            <p class="pars-definition">
              With capabilities such as a versioning system, ready-to-use data sets, comprehensive documentation,
              default value creation, API integrations, and compatibility with our other software, PARS provides a fast,
              seamless, and scalable integration experience.
            </p>
          </div>
        </section>

        <section class="section">
          <div class="pars-breakdown">

            <div class="pars-letter">
              <div class="letter-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                  <polyline points="3.27,6.96 12,12.01 20.73,6.96"></polyline>
                  <line x1="12" y1="22.08" x2="12" y2="12"></line>
                </svg>
              </div>
              <div class="letter-content">
                <h3>Process</h3>
                <p>Streamline your workflows and eliminate manual tasks with intelligent automation.</p>
              </div>
            </div>

            <div class="pars-letter">
              <div class="letter-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                </svg>
              </div>
              <div class="letter-content">
                <h3>Automation</h3>
                <p>Create custom business rules and logic without writing complex code.</p>
              </div>
            </div>

            <div class="pars-letter">
              <div class="letter-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M9 12l2 2 4-4"></path>
                  <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"></path>
                  <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"></path>
                  <path d="M3 12h6m6 0h6"></path>
                </svg>
              </div>
              <div class="letter-content">
                <h3>Rule</h3>
                <p>Create custom business rules and logic without writing complex code.</p>
              </div>
            </div>

            <div class="pars-letter">
              <div class="letter-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="18" cy="5" r="3"></circle>
                  <circle cx="6" cy="12" r="3"></circle>
                  <circle cx="18" cy="19" r="3"></circle>
                  <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
                  <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
                </svg>
              </div>
              <div class="letter-content">
                <h3>Service</h3>
                <p>Connect and orchestrate your existing services and applications seamlessly.</p>
              </div>
            </div>

          </div>
        </section>

        <section class="section">
          <h2>⭐ Core Features</h2>
          <div class="features-grid">
            <div class="feature-card">
              <div class="feature-icon">🎯</div>
              <h3>Flow Editor</h3>
              <p>Design complex business processes with an intuitive drag-and-drop interface. Every step is visually represented.</p>
            </div>

            <div class="feature-card">
              <div class="feature-icon">🔧</div>
              <h3>Multiple Node Types</h3>
              <p>Function, excel, database and many more node types for every operation.</p>
            </div>

            <div class="feature-card">
              <div class="feature-icon">⚡</div>
              <h3>Real-time Simulation</h3>
              <p>Test your designed flows instantly and see results in real-time.</p>
            </div>

            <div class="feature-card">
              <div class="feature-icon">🔗</div>
              <h3>API Integration</h3>
              <p>Easily integrate with other systems. Use with GRPC or HTTP.</p>
            </div>

            <div class="feature-card">
              <div class="feature-icon">👥</div>
              <h3>Multi-Organization</h3>
              <p>Manage multiple organizations, define admin roles and permissions.</p>
            </div>

            <div class="feature-card">
              <div class="feature-icon">📊</div>
              <h3>Detailed Reporting</h3>
              <p>Analyze with flow execution history, performance metrics and error logs.</p>
            </div>
          </div>
        </section>

        <section class="section">
          <h2>🎯 Getting Started</h2>
          <div class="getting-started-steps">
            <div class="step">
              <div class="step-number">1</div>
              <div class="step-content">
                <h4>Create your first workflow</h4>
                <p>Go to Workspace and create a simple "Hello World" workflow.</p>
              </div>
            </div>

            <div class="step">
              <div class="step-number">2</div>
              <div class="step-content">
                <h4>Test</h4>
                <p>Run your workflow in simulation environment and see the results.</p>
              </div>
            </div>

            <div class="step">
              <div class="step-number">3</div>
              <div class="step-content">
                <h4>Integrate it into your code</h4>
                <p>Integrate your workflow into your code using GRPC or HTTP.</p>
              </div>
            </div>
          </div>

          <div class="next-steps">
            <router-link :to="{ name: 'documentation-integration' }" class="next-step-btn">
              Service Integration →
            </router-link>
            <router-link :to="{ name: 'workspace' }" class="next-step-btn primary">
              Start Now →
            </router-link>
          </div>
        </section>

      </div>
    </div>
  </div>
</template>

<script setup>
</script>

<style scoped>
.what-is-pars {
  min-height: 100vh;
  background: #f8fafc;
}

.header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 2rem;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
}

.back-link {
  display: inline-flex;
  align-items: center;
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
  margin-bottom: 1rem;
  transition: color 0.2s;
}

.back-link:hover {
  color: #1d4ed8;
}

.header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: #1a202c;
}

.header p {
  font-size: 1.125rem;
  color: #64748b;
  margin: 0;
}

.content {
  padding: 3rem 2rem;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
}

.section {
  margin-bottom: 4rem;
}

.section h2 {
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 2rem 0;
  color: #1a202c;
}

.pars-intro {
  padding: 2rem 0;
}

.pars-definition {
  font-size: 1.125rem;
  line-height: 1.7;
  color: #374151;
  margin: 0 0 1rem 0;
}

.pars-breakdown {
  display: grid;
  gap: 2rem;
}

.pars-letter {
  display: flex;
  align-items: flex-start;
  gap: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.pars-letter:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.letter-icon {
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3);
}

.letter-icon svg {
  width: 2rem;
  height: 2rem;
}

.letter-content {
  flex: 1;
}

.letter-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  color: #1a202c;
}

.letter-content p {
  font-size: 1rem;
  line-height: 1.6;
  color: #64748b;
  margin: 0;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.feature-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #1a202c;
}

.feature-card p {
  color: #64748b;
  line-height: 1.6;
  margin: 0;
}

.use-cases {
  display: grid;
  gap: 2rem;
}

.use-case {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.use-case h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #1a202c;
}

.use-case p {
  color: #64748b;
  line-height: 1.6;
  margin: 0 0 1rem 0;
}

.example {
  background: #f1f5f9;
  padding: 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  color: #475569;
}

.getting-started-steps {
  display: grid;
  gap: 2rem;
  margin-bottom: 3rem;
}

.step {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.step-number {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.step-content h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #1a202c;
}

.step-content p {
  color: #64748b;
  line-height: 1.6;
  margin: 0;
}

.next-steps {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.next-step-btn {
  padding: 0.75rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s;
  border: 1px solid #e2e8f0;
  background: white;
  color: #374151;
}

.next-step-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.1);
}

.next-step-btn.primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-color: #3b82f6;
}

.next-step-btn.primary:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
}

@media (max-width: 768px) {
  .header {
    padding: 1.5rem 1rem;
  }

  .header h1 {
    font-size: 2rem;
  }

  .content {
    padding: 2rem 1rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .next-steps {
    flex-direction: column;
  }

  .pars-letter {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .letter-icon {
    width: 3rem;
    height: 3rem;
    margin: 0 auto;
  }

  .letter-icon svg {
    width: 1.5rem;
    height: 1.5rem;
  }
}
</style>
