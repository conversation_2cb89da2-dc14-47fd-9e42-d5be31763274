<template>
  <div class="integration">
    <div class="header">
      <div class="header-content">
        <router-link :to="{ name: 'documentation' }" class="back-link">
          ← {{ $t('routes.documentation') }}
        </router-link>
        <h1>{{ $t('routes.documentation-integration') }}</h1>
        <p>Integration guide for other services and systems</p>
      </div>
    </div>

    <div class="content">
      <div class="content-wrapper">
        <section class="section">
          <h2>🔗 Integration Overview</h2>
          <div class="overview-content">
            <p>
              The Pars platform can easily integrate with other systems using modern API standards.
              API integration can be done via gRPC and HTTP protocols, providing secure data exchange.
            </p>
            <div class="integration-types">
              <div class="integration-type">
                <h4>🚀 gRPC API</h4>
                <p>High-performance integration with gRPC protocol</p>
              </div>
              <div class="integration-type">
                <h4>📡 HTTP API</h4>
                <p>RESTful data exchange over HTTP protocol</p>
              </div>
            </div>
          </div>
        </section>

        <section class="section">
          <h2 class="integration-title">
            <img src="https://acquiring-admin.tapsilat.dev/favicon.ico" alt="Tapsilat" class="integration-icon" />
            Tapsilat Integration
          </h2>

          <div class="api-section">
            <p>Our system integrates easily with Tapsilat. You can connect PARS to Tapsilat without using configuration files or needing technical support.</p>
          </div>

          <div class="integration-steps-detailed">
            <div class="step-item">
              <div class="step-header">
                <span class="step-number">1</span>
                <h3>First, open Tapsilat Admin</h3>
              </div>
            </div>

            <div class="step-item">
              <div class="step-header">
                <span class="step-number">2</span>
                <h3>We need to add a feature flag. Organization > Features, add a feature flag named 'PARS_ACTIVE'. This will automatically activate PARS and make it available.</h3>
              </div>
              <div class="step-image-placeholder">
                <img src="../../../assets/images/feature_flag.png" alt="Feature Flag Screenshot" class="step-screenshot" @click="openImageInNewTab('/src/assets/images/feature_flag.png')">
                <img src="../../../assets/images/feature_flag_2.png" alt="Feature Flag Screenshot" class="step-screenshot" @click="openImageInNewTab('/src/assets/images/feature_flag_2.png')">
              </div>
            </div>

            <div class="step-item">
              <div class="step-header">
                <span class="step-number">3</span>
                <h3>Get an access token from the 'Access Tokens' Section in PARS</h3>
              </div>
              <div class="step-image-placeholder">
                <img src="../../../assets/images/access_tokens.png" alt="Access Tokens Screenshot" class="step-screenshot" @click="openImageInNewTab('/src/assets/images/access_tokens.png')">
              </div>
            </div>

            <div class="step-item">
              <div class="step-header">
                <span class="step-number">4</span>
                <h3>We need to add this access token with other configurations to Tapsilat Admin. Organization > Connect to PARS.</h3>
              </div>
              <div class="step-image-placeholder">
                <img src="../../../assets/images/connect_to_pars.png" alt="Connect to PARS Screenshot" class="step-screenshot" @click="openImageInNewTab('/src/assets/images/connect_to_pars.png')">
              </div>
            </div>

            <div class="step-item">
              <div class="step-header">
                <span class="step-number">5</span>
                <h3>If everything is okay, you will see connection success button.</h3>
              </div>
              <div class="step-image-placeholder">
                <img src="../../../assets/images/connected.png" alt="Connected Screenshot" class="step-screenshot" @click="openImageInNewTab('/src/assets/images/connected.png')">
              </div>
            </div>

            <div class="step-item">
              <div class="step-header">
                <span class="step-number">6</span>
                <h3>Then, you should go to 'Selected Simulates' under PARS.</h3>
              </div>
              <div class="step-image-placeholder">
                <img src="../../../assets/images/selected_simulates.png" alt="Selected Simulates Screenshot" class="step-screenshot" @click="openImageInNewTab('/src/assets/images/selected_simulates.png')">
              </div>
            </div>

            <div class="step-item">
              <div class="step-header">
                <span class="step-number">7</span>
                <h3>Finally, select the section you want to activate in Tapsilat Admin, then activate it and select the simulation.</h3>
              </div>
              <div class="step-image-placeholder">
                <img src="../../../assets/images/selected_simulates_2.png" alt="Selected Simulates 2 Screenshot" class="step-screenshot" @click="openImageInNewTab('/src/assets/images/selected_simulates_2.png')">
              </div>
            </div>
          </div>

        </section>

        <section class="section">
          <h2>🎯 Next Steps</h2>
          <div class="next-steps">
            <router-link to="/app/documentation/nodes" class="next-step-btn">
              Node Types →
            </router-link>
            <router-link to="/app/access-token-list" class="next-step-btn primary">
              Create Access Token →
            </router-link>
          </div>
        </section>

      </div>
    </div>


  </div>
</template>

<script setup>

const openImageInNewTab = (imageSrc) => {
  window.open(imageSrc, '_blank')
}
</script>

<style scoped>
.integration {
  min-height: 100vh;
  background: #f8fafc;
}

.header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 2rem;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
}

.back-link {
  display: inline-block;
  color: #3b82f6;
  text-decoration: none;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  transition: color 0.3s ease;
}

.back-link:hover {
  color: #1d4ed8;
}

.header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: #1a202c;
}

.header p {
  font-size: 1.125rem;
  color: #64748b;
  margin: 0;
}

.content {
  padding: 3rem 2rem;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
}

.section {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  margin-bottom: 2rem;
  border: 1px solid #e2e8f0;
}

.section h2 {
  font-size: 1.75rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
  color: #1a202c;
}

.overview-content p {
  font-size: 1.125rem;
  line-height: 1.7;
  color: #374151;
  margin: 0 0 2rem 0;
}

.integration-types {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.integration-type {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 1rem;
  text-align: center;
}

.integration-type h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #1a202c;
}

.integration-type p {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
}

.api-section {
  margin-bottom: 2rem;
}

.api-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #1a202c;
}

.api-section p {
  color: #64748b;
  margin: 0 0 1rem 0;
}

.code-block {
  background: #1a202c;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 6px;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 1rem 0;
}

.endpoints {
  display: grid;
  gap: 0.5rem;
}

.endpoint {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
}

.method {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  min-width: 3rem;
  text-align: center;
}

.method.get {
  background: #dcfce7;
  color: #166534;
}

.method.post {
  background: #dbeafe;
  color: #1e40af;
}

.method.grpc {
  background: #fef3c7;
  color: #92400e;
}

.path {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 500;
  color: #1a202c;
  flex: 1;
}

.desc {
  color: #64748b;
  font-size: 0.875rem;
}

.integration-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.integration-icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.integration-steps-detailed {
  display: grid;
  gap: 2rem;
  margin-top: 1.5rem;
}

.step-item {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1.5rem;
  background: #f8fafc;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.step-number {
  background: #3b82f6;
  color: white;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.step-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
  line-height: 1.5;
}

.step-image-placeholder {
  margin-top: 1rem;
  text-align: center;
}

.image-placeholder {
  background: #f1f5f9;
  border: 2px dashed #cbd5e1;
  border-radius: 6px;
  padding: 2rem;
  text-align: center;
  color: #64748b;
  font-style: italic;
}

.step-screenshot {
  display: block;
  width: 100%;
  max-width: 600px;
  height: auto;
  margin: 0 auto;
  margin-top: 15px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease;
}

.step-screenshot:hover {
  transform: scale(1.02);
}

.webhook-content p {
  color: #64748b;
  margin: 0 0 1.5rem 0;
}

.webhook-events h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #1a202c;
}

.webhook-events ul {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
}

.webhook-events li {
  padding: 0.5rem 0;
  border-bottom: 1px solid #f1f5f9;
  color: #64748b;
}

.webhook-events code {
  background: #f1f5f9;
  color: #1a202c;
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
  font-size: 0.875rem;
}

.webhook-example h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #1a202c;
}

.db-content p {
  color: #64748b;
  margin: 0 0 1.5rem 0;
}

.db-types {
  display: grid;
  gap: 1.5rem;
}

.db-type {
  border-left: 4px solid #3b82f6;
  padding-left: 1rem;
}

.db-type h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #1a202c;
}

.db-type p {
  color: #64748b;
  margin: 0 0 0.75rem 0;
  font-size: 0.875rem;
}

.connection-example {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 0.5rem;
  font-size: 0.875rem;
}

.connection-example code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color: #1a202c;
}

.common-integrations {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.integration-example {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 1.5rem;
  background: #f8fafc;
}

.integration-example h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  color: #1a202c;
}

.integration-example p {
  color: #64748b;
  margin: 0 0 1rem 0;
  font-size: 0.875rem;
}

.integration-steps {
  display: grid;
  gap: 0.5rem;
}

.step {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 0.5rem;
  font-size: 0.875rem;
  color: #374151;
}

.security-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.security-item h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #1a202c;
}

.security-item ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.security-item li {
  padding: 0.5rem 0;
  border-bottom: 1px solid #f1f5f9;
  color: #64748b;
  font-size: 0.875rem;
}

.security-item li:last-child {
  border-bottom: none;
}

.next-steps {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.next-step-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 1px solid #e2e8f0;
  background: white;
  color: #374151;
}

.next-step-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.next-step-btn.primary {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.next-step-btn.primary:hover {
  background: #2563eb;
}

@media (max-width: 768px) {
  .header h1 {
    font-size: 2rem;
  }

  .content {
    padding: 2rem 1rem;
  }

  .section {
    padding: 1.5rem;
  }

  .integration-types {
    grid-template-columns: 1fr;
  }

  .common-integrations {
    grid-template-columns: 1fr;
  }

  .security-content {
    grid-template-columns: 1fr;
  }

  .next-steps {
    flex-direction: column;
  }

  .endpoint {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
</style>
