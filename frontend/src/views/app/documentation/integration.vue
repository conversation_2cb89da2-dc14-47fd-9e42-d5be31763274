<template>
  <component :is="currentLanguageComponent" />
</template>

<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import IntegrationEn from './integration-en.vue'
import IntegrationTr from './integration-tr.vue'

const { locale } = useI18n()

const currentLanguageComponent = computed(() => {
  return locale.value === 'tr' ? IntegrationTr : IntegrationEn
})
</script>

<style scoped>
</style>
