<template>
  <div class="what-is-pars">
    <div class="header">
      <div class="header-content">
        <router-link :to="{ name: 'documentation' }" class="back-link">
          ← {{ $t('routes.documentation') }}
        </router-link>
        <h1>{{ $t('routes.documentation-what-is-pars') }}</h1>
        <p>Pars platformu ve temel özellikleri hakkında kapsamlı bilgi</p>
      </div>
    </div>

    <div class="content">
      <div class="content-wrapper">

        <section class="section">
          <h2>Pars Nedir?</h2>
          <div class="pars-intro">
            <p class="pars-definition">
              <strong>PARS</strong> (Process, Automation, Rule, Service) iş süreçlerinizi ve iş akışlarınızı
              otomatikleştirmenize yardımcı olan güçlü bir platformdur — hiçbir sınırlama olmadan özgürce geliştirme yapın.
              PARS, en karmaşık iş akışlarınızı tasarlamanıza ve yönetmenize yardımcı olmak için
              farklı node türleri ve gelişmiş özellikler sunar.
            </p>
            <p class="pars-definition">
              Versiyon sistemi, kullanıma hazır veri setleri, kapsamlı dokümantasyon, varsayılan değer oluşturma,
              API entegrasyonları ve diğer yazılımlarımızla uyumluluk gibi yeteneklerle PARS, hızlı,
              sorunsuz ve ölçeklenebilir bir entegrasyon deneyimi sağlar.
            </p>
          </div>
        </section>

        <section class="section">
          <div class="pars-breakdown">

            <div class="pars-letter">
              <div class="letter-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                  <polyline points="3.27,6.96 12,12.01 20.73,6.96"></polyline>
                  <line x1="12" y1="22.08" x2="12" y2="12"></line>
                </svg>
              </div>
              <div class="letter-content">
                <h3>Process (Süreç)</h3>
                <p>İş akışlarınızı düzenleyin ve manuel görevleri akıllı otomasyon ile ortadan kaldırın.</p>
              </div>
            </div>

            <div class="pars-letter">
              <div class="letter-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                </svg>
              </div>
              <div class="letter-content">
                <h3>Automation (Otomasyon)</h3>
                <p>Karmaşık kod yazmadan özel iş kuralları ve mantığı oluşturun.</p>
              </div>
            </div>

            <div class="pars-letter">
              <div class="letter-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M9 12l2 2 4-4"></path>
                  <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"></path>
                  <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"></path>
                  <path d="M3 12h6m6 0h6"></path>
                </svg>
              </div>
              <div class="letter-content">
                <h3>Rule (Kural)</h3>
                <p>Özel iş kuralları ve mantığı oluşturun, karmaşık kod yazmaya gerek yok.</p>
              </div>
            </div>

            <div class="pars-letter">
              <div class="letter-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="18" cy="5" r="3"></circle>
                  <circle cx="6" cy="12" r="3"></circle>
                  <circle cx="18" cy="19" r="3"></circle>
                  <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
                  <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
                </svg>
              </div>
              <div class="letter-content">
                <h3>Service (Servis)</h3>
                <p>Mevcut servislerinizi ve uygulamalarınızı sorunsuz bir şekilde bağlayın ve düzenleyin.</p>
              </div>
            </div>

          </div>
        </section>

        <section class="section">
          <h2>⭐ Temel Özellikler</h2>
          <div class="features-grid">

            <div class="feature-card">
              <div class="feature-icon">🎯</div>
              <h3>Flow Editörü</h3>
              <p>Arayüzden sürükle-bırak ile karmaşık iş süreçleri tasarlayın. Her adımınızı görselleştirin.</p>
            </div>

            <div class="feature-card">
              <div class="feature-icon">🔧</div>
              <h3>Çoklu Node Türleri</h3>
              <p>Function, excel, database ve her işlem için daha birçok node türü.</p>
            </div>

            <div class="feature-card">
              <div class="feature-icon">⚡</div>
              <h3>Gerçek Zamanlı Simülasyon</h3>
              <p>Tasarladığınız flow'ları anında test edin ve sonuçları gerçek zamanlı olarak görün.</p>
            </div>

            <div class="feature-card">
              <div class="feature-icon">🔗</div>
              <h3>API Entegrasyonu</h3>
              <p>GRPC veya HTTP ile diğer sistemlerle kolayca entegre olun.</p>
            </div>

            <div class="feature-card">
              <div class="feature-icon">👥</div>
              <h3>Çoklu Organizasyon</h3>
              <p>Birden fazla organizasyonu yönetin, admin rolleri ve izinleri tanımlayın.</p>
            </div>

            <div class="feature-card">
              <div class="feature-icon">📊</div>
              <h3>Detaylı Raporlama</h3>
              <p>Flow geçmişi, performans metrikleri ve hata logları ile analiz yapın.</p>
            </div>

          </div>
        </section>

        <section class="section">
          <h2>🎯 Başlangıç</h2>
          <div class="getting-started-steps">
            <div class="step">
              <div class="step-number">1</div>
              <div class="step-content">
                <h4>İlk iş akışınızı oluşturun</h4>
                <p>Workspace'e gidin ve basit bir "Merhaba Dünya" iş akışı oluşturun.</p>
              </div>
            </div>

            <div class="step">
              <div class="step-number">2</div>
              <div class="step-content">
                <h4>Test Edin</h4>
                <p>İş akışınızı simülasyon ortamında çalıştırın ve sonuçları görün.</p>
              </div>
            </div>

            <div class="step">
              <div class="step-number">3</div>
              <div class="step-content">
                <h4>Kodunuza entegre edin</h4>
                <p>İş akışınızı GRPC veya HTTP kullanarak kodunuza entegre edin.</p>
              </div>
            </div>
          </div>

          <div class="next-steps">
            <router-link :to="{ name: 'documentation-integration' }" class="next-step-btn">
              Servis Entegrasyonu →
            </router-link>
            <router-link :to="{ name: 'workspace' }" class="next-step-btn primary">
              Hemen Başla →
            </router-link>
          </div>
        </section>

      </div>
    </div>
  </div>
</template>

<script setup>
</script>

<style scoped>
.what-is-pars {
  min-height: 100vh;
  background: #f8fafc;
}

.header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 2rem;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
}

.back-link {
  display: inline-flex;
  align-items: center;
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
  margin-bottom: 1rem;
  transition: color 0.2s;
}

.back-link:hover {
  color: #1d4ed8;
}

.header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: #1a202c;
}

.header p {
  font-size: 1.125rem;
  color: #64748b;
  margin: 0;
}

.content {
  padding: 3rem 2rem;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
}

.section {
  margin-bottom: 4rem;
}

.section h2 {
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 2rem 0;
  color: #1a202c;
}

.pars-intro {
  padding: 2rem 0;
}

.pars-definition {
  font-size: 1.125rem;
  line-height: 1.7;
  color: #374151;
  margin: 0 0 1rem 0;
}

.pars-breakdown {
  display: grid;
  gap: 2rem;
}

.pars-letter {
  display: flex;
  align-items: flex-start;
  gap: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.pars-letter:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.letter-icon {
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3);
}

.letter-icon svg {
  width: 2rem;
  height: 2rem;
}

.letter-content {
  flex: 1;
}

.letter-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  color: #1a202c;
}

.letter-content p {
  font-size: 1rem;
  line-height: 1.6;
  color: #64748b;
  margin: 0;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.feature-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #1a202c;
}

.feature-card p {
  color: #64748b;
  line-height: 1.6;
  margin: 0;
}

.use-cases {
  display: grid;
  gap: 2rem;
}

.use-case {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.use-case h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #1a202c;
}

.use-case p {
  color: #64748b;
  line-height: 1.6;
  margin: 0 0 1rem 0;
}

.example {
  background: #f1f5f9;
  padding: 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  color: #475569;
}

.getting-started-steps {
  display: grid;
  gap: 2rem;
  margin-bottom: 3rem;
}

.step {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.step-number {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.step-content h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #1a202c;
}

.step-content p {
  color: #64748b;
  line-height: 1.6;
  margin: 0;
}

.next-steps {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.next-step-btn {
  padding: 0.75rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s;
  border: 1px solid #e2e8f0;
  background: white;
  color: #374151;
}

.next-step-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.1);
}

.next-step-btn.primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-color: #3b82f6;
}

.next-step-btn.primary:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
}

@media (max-width: 768px) {
  .header {
    padding: 1.5rem 1rem;
  }

  .header h1 {
    font-size: 2rem;
  }

  .content {
    padding: 2rem 1rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .next-steps {
    flex-direction: column;
  }

  .pars-letter {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .letter-icon {
    width: 3rem;
    height: 3rem;
    margin: 0 auto;
  }

  .letter-icon svg {
    width: 1.5rem;
    height: 1.5rem;
  }
}
</style>
