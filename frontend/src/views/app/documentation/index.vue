<template>
  <div class="documentation-container">
    <!-- Header Section -->
    <div class="doc-header">
      <div class="header-content">
        <div class="header-icon">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14,2 14,8 20,8"></polyline>
            <line x1="16" y1="13" x2="8" y2="13"></line>
            <line x1="16" y1="17" x2="8" y2="17"></line>
            <polyline points="10,9 9,9 8,9"></polyline>
          </svg>
        </div>
        <h1 class="doc-title">{{ $t('documentation.index.title') }}</h1>
        <p class="doc-subtitle">{{ $t('documentation.index.subtitle') }}</p>
      </div>
    </div>

    <div class="doc-content">
      <div class="content-wrapper">
        <div class="doc-sections">
          <section class="doc-section">
            <h2>{{ $t('documentation.sections.start.title') }}</h2>
            <div class="section-links">
              <router-link :to="{ name: 'documentation-what-is-pars' }" class="section-link">
                <span class="link-title">{{ $t('documentation.sections.start.what_is_pars') }}</span>
                <span class="link-desc">{{ $t('documentation.sections.start.what_is_pars_desc') }}</span>
              </router-link>
              <router-link :to="{ name: 'documentation-integration' }"  class="section-link">
                <span class="link-title">{{ $t('documentation.sections.start.integration') }}</span>
                <span class="link-desc">{{ $t('documentation.sections.start.integration_desc') }}</span>
              </router-link>
            </div>
          </section>

          <section class="doc-section">
            <h2>{{ $t('documentation.sections.flow_editor.title') }}</h2>
            <div class="section-links">
              <router-link :to="{ name: 'documentation-nodes' }" class="section-link">
                <span class="link-title">{{ $t('documentation.sections.flow_editor.node_types') }}</span>
                <span class="link-desc">{{ $t('documentation.sections.flow_editor.node_types_desc') }}</span>
              </router-link>
              <router-link to="/app/workspace" class="section-link">
                <span class="link-title">{{ $t('documentation.sections.flow_editor.workspace') }}</span>
                <span class="link-desc">{{ $t('documentation.sections.flow_editor.workspace_desc') }}</span>
              </router-link>
            </div>
          </section>

        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
</script>

<style scoped>
.documentation-container {
  min-height: 100vh;
  background: #f8fafc;
}

.doc-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 3rem 2rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.doc-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  background-image:
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.header-icon {
  width: 3.5rem;
  height: 3.5rem;
  margin: 0 auto 1rem auto;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.header-icon svg {
  width: 2rem;
  height: 2rem;
}

.doc-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.75rem 0;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.doc-subtitle {
  font-size: 1.125rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}



.doc-content {
  padding: 3rem 2rem;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
}

.doc-sections {
  display: grid;
  gap: 3rem;
}

.doc-section {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  border: 1px solid #e2e8f0;
}

.doc-section h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
  color: #1a202c;
  border-bottom: 2px solid #f1f5f9;
  padding-bottom: 0.5rem;
}

.section-links {
  display: grid;
  gap: 1rem;
}

.section-link {
  display: block;
  padding: 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  text-decoration: none;
  color: inherit;
  transition: all 0.2s ease;
  background: #f8fafc;
}

.section-link:hover {
  border-color: #3b82f6;
  background: #eff6ff;
  transform: translateX(4px);
}

.link-title {
  display: block;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 0.25rem;
}

.link-desc {
  display: block;
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .doc-header {
    padding: 2.5rem 1rem;
  }

  .doc-title {
    font-size: 2rem;
  }

  .doc-subtitle {
    font-size: 1rem;
  }

  .doc-content {
    padding: 2rem 1rem;
  }

  .doc-section {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .doc-header {
    padding: 2rem 1rem;
  }

  .doc-title {
    font-size: 1.75rem;
  }

  .header-icon {
    width: 3rem;
    height: 3rem;
  }

  .header-icon svg {
    width: 1.5rem;
    height: 1.5rem;
  }
}
</style>