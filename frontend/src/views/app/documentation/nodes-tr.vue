<template>
  <div class="nodes-documentation">
    <div class="nodes-header">
      <div class="header-content">
        <router-link :to="{ name: 'documentation' }" class="back-link">
          ← Dökümantasyon
        </router-link>
        <h1>Node Dökümantasyonu</h1>
        <p>Flow editor'da kullanılabilen tüm node türleri ve özellikleri</p>
      </div>
    </div>

    <div class="nodes-content">
      <div class="nodes-grid">
        
        <!-- Start Node -->
        <div class="node-card start-node">
          <div class="node-header">
            <div class="node-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M5 12h14"></path>
                <path d="m12 5 7 7-7 7"></path>
              </svg>
            </div>
            <h3>Start Node</h3>
            <span class="node-badge">Başlangıç</span>
          </div>
          <div class="node-description">
            <p>Flow'un başlangıç noktasıdır. Tüm akışlar bu node ile başlar ve gelen veriyi sonraki node'lara iletir. Her akışta sadece bir adet başlangıç noktası bulunur.</p>
            <div class="node-features">
              <div class="feature">✓ Flow başlangıç noktası</div>
              <div class="feature">✓ Veri girişi</div>
            </div>
          </div>
        </div>

        <!-- End Node -->
        <div class="node-card end-node">
          <div class="node-header">
            <div class="node-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M19 12H5"></path>
                <path d="m12 19-7-7 7-7"></path>
              </svg>
            </div>
            <h3>End Node</h3>
            <span class="node-badge">Bitiş</span>
          </div>
          <div class="node-description">
            <p>Flow'un bitiş noktasıdır. Akışı sonlandırır ve son veriyi çıktı olarak döndürür.</p>
            <div class="node-features">
              <div class="feature">✓ Flow bitiş noktası</div>
              <div class="feature">✓ Veri çıktısı</div>
            </div>
          </div>
        </div>

        <!-- Function Node -->
        <div class="node-card function-node">
          <div class="node-header">
            <div class="node-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M9 12l2 2 4-4"/>
                <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"/>
                <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"/>
                <path d="M3 12h6m6 0h6"/>
              </svg>
            </div>
            <h3>Function Node</h3>
            <span class="node-badge">JavaScript</span>
          </div>
          <div class="node-description">
            <p>JavaScript kodu çalıştırmak için kullanılır. Özel fonksiyonlar yazabilir ve veri işleme yapabilirsiniz.</p>
            <div class="node-features">
              <div class="feature">✓ Js Editör</div>
              <div class="feature">✓ dayjs - Lightweight date manipulation library</div>
              <div class="feature">✓ big.js - Arbitrary-precision decimal arithmetic</div>
              <div class="feature">✓ zod - TypeScript-first schema validation</div>
              <div class="feature">✓ http - HTTP client for making API requests</div>
              <div class="feature">✓ zen - Expression evaluator and decision engine</div>
            </div>
          </div>
          <div class="node-example">
            <h4>Başlangıç Kullanım:</h4>
            <pre><code>import zen from 'zen';

/** @type {Handler} **/
export const handler = async (input) => {  
  return result;
};</code></pre>
          </div>
        </div>

        <!-- Switch Node -->
        <div class="node-card switch-node">
          <div class="node-header">
            <div class="node-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M12 2 L20 12 L12 22 L4 12 Z" fill="currentColor" stroke="none"/>
                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" stroke="white" stroke-width="1.5" fill="none"/>
                <circle cx="12" cy="17" r="1" fill="white"/>
              </svg>
            </div>
            <h3>Switch Node</h3>
            <span class="node-badge">Koşul</span>
          </div>
          <div class="node-description">
            <p>Koşullu dallanma için kullanılır. Farklı koşullara göre akışı farklı yönlere yönlendirir.</p>
            <div class="node-features">
              <div class="feature">✓ Çoklu koşul desteği</div>
              <div class="feature">✓ Boolean ifadeler</div>
              <div class="feature">✓ First/Collect modları</div>
            </div>
          </div>

        </div>

        <!-- Timeout Node -->
        <div class="node-card timeout-node">
          <div class="node-header">
            <div class="node-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <polyline points="12,6 12,12 16,14"/>
              </svg>
            </div>
            <h3>Timeout Node</h3>
            <span class="node-badge">Timeout</span>
          </div>
          <div class="node-description">
            <p>Belirli bir süre bekletme işlemi yapar. 1-15 saniye arası ayarlanabilir gecikme süresi.</p>
            <div class="node-features">
              <div class="feature">✓ 1-15 saniye arası</div>
              <div class="feature">✓ Görsel progress bar</div>
            </div>
          </div>
        </div>

        <!-- AI Agent Node -->
        <div class="node-card ai-agent-node">
          <div class="node-header">
            <div class="node-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M12 2a2 2 0 0 1 2 2c0 .74-.4 1.39-1 1.73V7h4a2 2 0 0 1 2 2v1.28c.6.35 1 .99 1 1.72 0 .73-.4 1.37-1 1.72V15a2 2 0 0 1-2 2h-4v1.27c.6.34 1 .99 1 1.73a2 2 0 1 1-4 0c0-.74.4-1.39 1-1.73V17H7a2 2 0 0 1-2-2v-1.28c-.6-.35-1-.99-1-1.72 0-.73.4-1.37 1-1.72V9a2 2 0 0 1 2-2h4V5.73c-.6-.34-1-.99-1-1.73a2 2 0 0 1 2-2z"/>
              </svg>
            </div>
            <h3>AI Agent Node</h3>
            <span class="node-badge">Yapay Zeka</span>
          </div>
          <div class="node-description">
            <p>PARS AI Agent'larını kullanarak yapay zeka destekli işlemler yapar. Önceden tanımlanmış agent'ları seçebilirsiniz.</p>
            <div class="node-features">
              <div class="feature">✓ Önceden tanımlı agent'lar</div>
              <div class="feature">✓ AI destekli karar verme</div>
              <div class="feature">✓ Dinamik agent seçimi</div>
            </div>
          </div>
        </div>

        <!-- Excel Node -->
        <div class="node-card excel-node">
          <div class="node-header">
            <div class="node-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
                <polyline points="14,2 14,8 20,8"/>
                <path d="M16 13H8"/>
                <path d="M16 17H8"/>
                <path d="M10 9H8"/>
              </svg>
            </div>
            <h3>Excel Node</h3>
            <span class="node-badge">Excel</span>
          </div>
          <div class="node-description">
            <p>Excel ve CSV dosyalarını işlemek için kullanılır. Dosya yükleme, veri okuma ve sütun seçimi yapabilirsiniz.</p>
            <div class="node-features">
              <div class="feature">✓ Excel (.xlsx, .xls) desteği</div>
              <div class="feature">✓ CSV dosya desteği</div>
              <div class="feature">✓ Sütun seçimi</div>
              <div class="feature">✓ Veri limiti ayarı</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="best-practices">
      <div class="practices-content">
        <h2>En İyi Uygulamalar</h2>
        <div class="practices-grid">
          <div class="practice-item">
            <h4>🎯 Node Seçimi</h4>
            <p>Her görev için en uygun node türünü seçin. Function node'u sadece özel işlemler için kullanın.</p>
          </div>
          <div class="practice-item">
            <h4>🔗 Bağlantılar</h4>
            <p>Node'lar arasındaki veri akışını net tutun. Gereksiz bağlantılardan kaçının.</p>
          </div>
          <div class="practice-item">
            <h4>⚡ Performans</h4>
            <p>Büyük veri setleri için uygun node'ları kullanın. Timeout değerlerini dikkatli ayarlayın.</p>
          </div>
          <div class="practice-item">
            <h4>🛡️ Hata Yönetimi</h4>
            <p>Her kritik node için hata yakalama mekanizması ekleyin. Switch node ile hata kontrolü yapın.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
</script>

<style scoped>
.nodes-documentation {
  min-height: 100vh;
  background: #f8fafc;
}

.nodes-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 2rem;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
}

.back-link {
  display: inline-block;
  color: #3b82f6;
  text-decoration: none;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  transition: color 0.3s ease;
}

.back-link:hover {
  color: #1d4ed8;
}

.nodes-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: #1a202c;
}

.nodes-header p {
  font-size: 1.125rem;
  color: #64748b;
  margin: 0;
}

.nodes-content {
  padding: 4rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.nodes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.node-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.node-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.node-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.node-icon {
  width: 3rem;
  height: 3rem;
  padding: 0.75rem;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.start-node .node-icon {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.end-node .node-icon {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.function-node .node-icon {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
}

.switch-node .node-icon {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
}

.timeout-node .node-icon {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
}

.ai-agent-node .node-icon {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
}

.excel-node .node-icon {
  background: linear-gradient(135deg, #059669, #047857);
  color: white;
}

.http-node .node-icon {
  background: linear-gradient(135deg, #0891b2, #0e7490);
  color: white;
}

.database-node .node-icon {
  background: linear-gradient(135deg, #7c3aed, #6d28d9);
  color: white;
}

.sms-node .node-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.mail-node .node-icon {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
}

.node-icon svg {
  width: 1.5rem;
  height: 1.5rem;
}

.node-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: #1a202c;
  flex: 1;
}

.node-badge {
  background: #e2e8f0;
  color: #64748b;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.node-description p {
  color: #64748b;
  line-height: 1.6;
  margin: 0 0 1.5rem 0;
}

.node-features {
  display: grid;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.feature {
  color: #059669;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.node-example {
  border-top: 1px solid #e2e8f0;
  padding-top: 1.5rem;
}

.node-example h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #1a202c;
}

.node-example pre {
  background: #1a202c;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 8px;
  overflow-x: auto;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
}

.best-practices {
  background: white;
  padding: 4rem 2rem;
}

.practices-content {
  max-width: 1200px;
  margin: 0 auto;
}

.best-practices h2 {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 3rem 0;
  color: #1a202c;
}

.practices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.practice-item {
  text-align: center;
  padding: 2rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.practice-item h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #1a202c;
}

.practice-item p {
  color: #64748b;
  line-height: 1.6;
  margin: 0;
}

@media (max-width: 768px) {
  .nodes-header h1 {
    font-size: 2rem;
  }

  .nodes-grid {
    grid-template-columns: 1fr;
  }

  .node-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .practices-grid {
    grid-template-columns: 1fr;
  }
}
</style>
