<template>
  <div v-if="loading" class="max-w-7xl mx-auto flex justify-center items-center py-10">
    <MonoIcon class="size-20 animate-spin" name="spin" />
  </div>
  <div v-else-if="error" class="max-w-7xl mx-auto flex gap-2 justify-center items-center py-10">
    <Error404 v-if="error?.code === '404'" />
    <Error500 v-else-if="error?.code === '500'" />
    <ErrorCustom v-else :code="error?.code || 500" :title="error?.title || 'Unexpected Error'"
      :message="error?.message || 'An unexpected error occurred. Please try again later.'">
      <template #action>
        <button type="button" class="mt-10 text-sm/7 font-semibold text-indigo-600 flex items-center gap-2"
          @click.prevent="$router.go(0)">
          <MonoIcon name="refresh" class="size-4" />
          Retry
        </button>
      </template>
    </ErrorCustom>
  </div>
  <div class="max-w-7xl mx-auto" v-else>
    <div
      class="p-5 pb-0 bg-white border border-gray-200 shadow-sm rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
      <figure>
        <svg class="w-full rounded-lg" preserveAspectRatio="none" width="1113" height="161" viewBox="0 0 1113 161"
          fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#clip0_666_220723)">
            <rect x="1" width="1112" height="348" fill="#818cf8"></rect>
            <path d="M512.694 359.31C547.444 172.086 469.835 34.2204 426.688 -11.3096H1144.27V359.31H512.694Z"
              fill="#6366f1"></path>
            <path
              d="M818.885 185.745C703.515 143.985 709.036 24.7949 726.218 -29.5801H1118.31V331.905C1024.49 260.565 963.098 237.945 818.885 185.745Z"
              fill="#4f46e5"></path>
          </g>
        </svg>
      </figure>

      <div class="-mt-28 lg:-mt-16 mx-4 flex items-start gap-4">
        <div
          class="shrink-0 relative flex w-[60px] h-[60px] md:w-[120px] md:h-[120px] border-4 border-neutral-200 rounded-xl dark:border-neutral-700 overflow-hidden shadow-xl">
          <div class="flex justify-center bg-indigo-500 items-center size-full">
            <MonoIcon class="size-20 text-white" name="bank" />
            <!-- todo: show org icon -->
          </div>
        </div>

        <div class="h-[120px] overflow-hidden">
          <div class="lg:h-[60px] flex items-end">
            <h1 class="md:text-2xl capitalize font-semibold text-white">
              {{ organization.name }}
            </h1>
          </div>
          <div
            class="lg:h-[60px] group flex gap-2 items-start pt-2 text-white lg:text-gray-500 lg:dark:text-neutral-500">
            <p class="h-4 text-xs md:text-sm whitespace-nowrap overflow-hidden text-ellipsis">
              ID: {{ organization.id }}
            </p>
            <span class="cursor-pointer lg:invisible lg:group-hover:visible" @click="copyOrganizationId">
              <MonoIcon class="mr-2 size-4" :name="orgIdCopied ? 'copy-check' : 'copy'" />
              <span class="sr-only">copy id</span>
            </span>
          </div>
        </div>
      </div>

      <div
        class="py-2 flex flex-row justify-between items-center gap-x-2 whitespace-nowrap overflow-x-auto overflow-y-hidden [&amp;::-webkit-scrollbar]:h-2 [&amp;::-webkit-scrollbar-thumb]:rounded-full [&amp;::-webkit-scrollbar-track]:bg-gray-100 [&amp;::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&amp;::-webkit-scrollbar-track]:bg-neutral-700 dark:[&amp;::-webkit-scrollbar-thumb]:bg-neutral-500">
        <t-profile-tabs @update:modelValue="(id) => setCurrentTab(id, true)" :tabs="tabs.filter((tab) => !tab.hide)"
          v-model="currentTabId" />

        <div class="">
          <button type="button"
            class="px-2.5 py-1.5 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"
            @click="setCurrentTab('settings', true)" :class="{
              'bg-gray-50 text-indigo-600 dark:bg-neutral-900 dark:text-white dark:hover:bg-gray-700':
                currentTabId === 'settings',
            }">
            <MonoIcon class="mr-2 size-5" name="cog" />
            {{ t("organizations.settings.title") }}
          </button>
        </div>
      </div>
    </div>

    <div class="mt-5">
      <component :is="currentTab.component" :key="currentTab.id" :detail="organization" />
    </div>
  </div>
</template>

<script setup>
import { t } from "@/plugins/i18n";
import { useOrganizationStore } from "@/store/submodules/organization";
import { defineAsyncComponent } from "vue";

const store = useOrganizationStore();
const route = useRoute();
const router = useRouter();
const toast = inject("toast");

const error = ref(null);
const loading = ref(true);

const organization = ref(null);

const tabs = [
  {
    id: "overview",
    component: defineAsyncComponent(() => import("@/components/organizations/organization-overview.vue")),
    title: t("organizations.overview.title"),
    icon: "user",
    isActive: false,
    disabled: false,
  },
  {
    id: "settings",
    component: defineAsyncComponent(() => import("@/components/organizations/organization-settings.vue")),
    title: t("organizations.settings.title"),
    icon: "cog",
    isActive: false,
    disabled: false,
    hide: true,
  },
];
const setCurrentTab = (tab, withId = false) => {
  router.push({
    query: {
      tab: tab,
      sub: tab == "settings" ? (route.query.sub ?? "general") : undefined,
    },
    replace: true,
  });

  if (withId) {
    let selectedTab = tabs.find((tabObject) => tabObject.id == tab);
    selectedTab ? (currentTab.value = selectedTab) : "";
    currentTabId.value = tab;
  } else {
    currentTab.value = tab;
    currentTabId.value = tab.id;
  }
};

const currentTabId = ref(null);
const currentTab = ref(null);

const orgIdCopied = ref(false);
const copyOrganizationId = (e) => {
  navigator.clipboard.writeText(organization.value.id);
  orgIdCopied.value = true;
  toast.success("Organization ID copied to clipboard");
  setTimeout(() => {
    orgIdCopied.value = false;
  }, 2000);
};

onMounted(async () => {
  setCurrentTab(route.query.tab ? route.query.tab : tabs[0].id, true);

  try {
    const res = await store.Detail(route.params.id);
    organization.value = res;
  } catch (err) {
    if (err?.response?.status === 404) {
      error.value = {
        code: "404",
        title: "Organization not found",
        message: "The organization you are looking for does not exist.",
      };
    } else {
      error.value = {
        code: "500",
        title: "Server Error",
        message: "An unexpected error occurred. Please try again later.",
      };
    }
  }

  loading.value = false;
});
</script>
