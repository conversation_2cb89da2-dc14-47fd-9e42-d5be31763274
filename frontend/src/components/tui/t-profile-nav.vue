<template>
  <nav class="flex gap-x-1">
    <router-link :to="{ name: 'profile' }" exact exact-active-class="bg-gray-100 dark:bg-gray-900"
      class="px-2.5 py-1.5 transition-colors relative inline-flex items-center gap-x-2 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700  hover:text-gray-800 text-sm rounded-lg focus:outline-none  text-gray-800 ">
      <svg class="mr-1 size-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z">
        </path>
      </svg>

      <span class="truncate">{{ $t('profile.tabs.profile') }}</span>
    </router-link>
  </nav>
</template>

<script setup>

</script>
