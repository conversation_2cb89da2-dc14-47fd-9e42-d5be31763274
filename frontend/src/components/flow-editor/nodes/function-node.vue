<template>
  <div class="function-node">
    <Handle
      id="input"
      type="target"
      :position="Position.Left"
      :style="{ background: '#555' }"
    />

    <div class="function-node-header">
      <div class="function-node-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M3 3h18v18H3V3zm16.525 13.707c-.131-.821-.666-1.511-2.252-2.155-.552-.259-1.165-.438-1.349-.854-.068-.248-.078-.382-.034-.529.113-.484.687-.629 1.137-.495.293.09.563.315.732.676.775-.507.775-.507 1.316-.844-.203-.314-.304-.451-.439-.586-.473-.528-1.103-.798-2.126-.77l-.528.067c-.507.124-.991.395-1.283.754-.855.968-.608 2.655.427 3.354 1.023.765 2.521.933 2.712 1.653.18.878-.652 1.159-1.475 1.058-.607-.136-.945-.439-1.316-1.002l-1.372.788c.157.359.337.517.607.832 1.305 1.316 4.568 1.249 5.153-.754.021-.067.18-.528.056-1.237l.034.049zm-6.737-5.434h-1.686c0 1.453-.007 2.898-.007 4.354 0 .924.047 1.772-.104 2.033-.247.517-.886.451-1.175.359-.297-.146-.448-.349-.623-.641-.047-.078-.082-.146-.095-.146l-1.368.844c.229.473.563.879.994 1.137.641.383 1.502.507 2.404.305.588-.17 1.095-.519 1.358-1.059.384-.697.302-1.553.299-2.509.008-1.541 0-3.083 0-4.635l.003-.042z"/>
        </svg>
      </div>
      <div class="function-node-title" v-if="!isEditing" @click="startEditing">
        {{ nodeLabel || 'Function' }}
      </div>
      <input v-else ref="titleInput" v-model="editableLabel" class="title-input" @blur="stopEditing"
        @keyup.enter="stopEditing" @keyup.esc="cancelEditing" type="text" />

      <div class="node-menu" @click.stop>
        <button class="menu-trigger" @click="toggleMenu" :class="{ active: showMenu }">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="1"></circle>
            <circle cx="12" cy="5" r="1"></circle>
            <circle cx="12" cy="19" r="1"></circle>
          </svg>
        </button>

        <div v-if="showMenu" class="menu-dropdown" @click.stop>
          <button class="menu-item" @click="openDocumentation">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14,2 14,8 20,8"></polyline>
            </svg>
            Documentation
          </button>

          <button class="menu-item delete" @click="deleteNode">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="3,6 5,6 21,6"></polyline>
              <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
            </svg>
            Delete
          </button>
        </div>
      </div>
    </div>

    <div class="function-node-content" @dblclick="openEditor">
      <div class="edit-hint">Double-click to edit code</div>
    </div>

    <Handle
      id="output"
      type="source"
      :position="Position.Right"
      :style="{ background: '#555' }"
    />

    <!-- Function Editor Modal -->
    <Teleport to="body">
      <div v-if="showEditor" class="editor-overlay" @click="closeEditor" @mousedown.stop @mousemove.stop @mouseup.stop>
        <div class="editor-modal" @click.stop @mousedown.stop @mousemove.stop @mouseup.stop>
        <!-- Header -->
        <header class="editor-header">
          <h2 class="editor-title">Function</h2>
          <button @click="closeEditor" class="close-button">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </header>

        <!-- Main Content -->
        <main class="editor-main">
          <!-- Code Editor Section -->
          <div class="editor-section">
            <div ref="editorContainer" class="codemirror-container"></div>
          </div>

          <!-- Console Section -->
          <div class="console-section">
            <div class="console-header">
              <h3 class="console-title">Console</h3>
              <button @click="clearConsole" class="console-clear-btn">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="3,6 5,6 21,6"></polyline>
                  <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                </svg>
              </button>
            </div>
            <div class="console-content">
              <div v-for="(log, index) in consoleLogs" :key="index" class="console-log">
                <span class="console-arrow">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="9,18 15,12 9,6"></polyline>
                  </svg>
                </span>
                <pre class="console-text">{{ log }}</pre>
              </div>
              <div v-if="consoleLogs.length === 0" class="console-empty">
                Console output will appear here...
              </div>
            </div>
          </div>
        </main>

        <!-- Footer -->
        <footer class="editor-footer">
          <button @click="closeEditor" class="footer-btn footer-btn-secondary">
            Close
          </button>
          <button @click="saveFunction" class="footer-btn footer-btn-primary">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
              <polyline points="17,21 17,13 7,13 7,21"></polyline>
              <polyline points="7,3 7,8 15,8"></polyline>
            </svg>
            Save
          </button>
        </footer>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup>
import { ref, nextTick, onUnmounted } from 'vue'
import { Handle, Position, useVueFlow } from '@vue-flow/core'
import { EditorView } from '@codemirror/view'
import { EditorState } from '@codemirror/state'
import { javascript } from '@codemirror/lang-javascript'
import { basicSetup } from 'codemirror'

// Light theme definition for the editor
const lightTheme = EditorView.theme({
  '&': {
    color: '#1f2937',
    backgroundColor: '#ffffff'
  },
  '.cm-content': {
    padding: '16px',
    caretColor: '#1f2937'
  },
  '.cm-focused .cm-cursor': {
    borderLeftColor: '#1f2937'
  },
  '.cm-focused .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection': {
    backgroundColor: '#dbeafe'
  },
  '.cm-panels': {
    backgroundColor: '#f9fafb',
    color: '#1f2937'
  },
  '.cm-panels.cm-panels-top': {
    borderBottom: '2px solid #4f46e5'
  },
  '.cm-panels.cm-panels-bottom': {
    borderTop: '2px solid #4f46e5'
  },
  '.cm-searchMatch': {
    backgroundColor: '#fef3c7',
    outline: '1px solid #f59e0b'
  },
  '.cm-searchMatch.cm-searchMatch-selected': {
    backgroundColor: '#fde68a'
  },
  '.cm-activeLine': {
    backgroundColor: '#f8fafc'
  },
  '.cm-selectionMatch': {
    backgroundColor: '#fef3c7'
  },
  '.cm-matchingBracket, .cm-nonmatchingBracket': {
    backgroundColor: '#e0e7ff',
    outline: '1px solid #a5b4fc'
  },
  '.cm-gutters': {
    backgroundColor: '#f9fafb',
    color: '#6b7280',
    border: 'none'
  },
  '.cm-activeLineGutter': {
    backgroundColor: '#f1f5f9'
  },
  '.cm-foldPlaceholder': {
    backgroundColor: 'transparent',
    border: 'none',
    color: '#6b7280'
  },
  '.cm-tooltip': {
    border: '1px solid #e5e7eb',
    backgroundColor: '#ffffff',
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
  },
  '.cm-tooltip-autocomplete': {
    '& > ul > li[aria-selected]': {
      backgroundColor: '#f3f4f6',
      color: '#1f2937'
    }
  }
}, { dark: false })

// Dark theme definition for the editor
const darkTheme = EditorView.theme({
  '&': {
    color: '#f9fafb',
    backgroundColor: '#1f2937'
  },
  '.cm-content': {
    padding: '16px',
    caretColor: '#f9fafb'
  },
  '.cm-focused .cm-cursor': {
    borderLeftColor: '#f9fafb'
  },
  '.cm-focused .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection': {
    backgroundColor: '#374151'
  },
  '.cm-panels': {
    backgroundColor: '#111827',
    color: '#f9fafb'
  },
  '.cm-panels.cm-panels-top': {
    borderBottom: '2px solid #4f46e5'
  },
  '.cm-panels.cm-panels-bottom': {
    borderTop: '2px solid #4f46e5'
  },
  '.cm-searchMatch': {
    backgroundColor: '#fbbf24',
    outline: '1px solid #f59e0b'
  },
  '.cm-searchMatch.cm-searchMatch-selected': {
    backgroundColor: '#fcd34d'
  },
  '.cm-activeLine': {
    backgroundColor: '#374151'
  },
  '.cm-selectionMatch': {
    backgroundColor: '#fbbf24'
  },
  '.cm-matchingBracket, .cm-nonmatchingBracket': {
    backgroundColor: '#4338ca',
    outline: '1px solid #6366f1'
  },
  '.cm-gutters': {
    backgroundColor: '#1f2937',
    color: '#9ca3af',
    border: 'none'
  },
  '.cm-activeLineGutter': {
    backgroundColor: '#374151'
  },
  '.cm-foldPlaceholder': {
    backgroundColor: 'transparent',
    border: 'none',
    color: '#9ca3af'
  },
  '.cm-tooltip': {
    border: '1px solid #374151',
    backgroundColor: '#1f2937',
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.3)'
  },
  '.cm-tooltip-autocomplete': {
    '& > ul > li[aria-selected]': {
      backgroundColor: '#374151',
      color: '#f9fafb'
    }
  }
}, { dark: true })

const props = defineProps({
  id: String,
  data: Object,
})

const emit = defineEmits(['update:data'])

const { removeNodes } = useVueFlow()

// Editor state
const showEditor = ref(false)
const jsCode = ref(props.data?.jsCode || "function process(event) {\n    // Your JavaScript code here\n    // 'event' is the input data for this function\n    console.log('Processing event:', event);\n    const result = {\n        ...event,\n        processed: true,\n        timestamp: new Date().toISOString()\n    };\n    return result;\n}")
const nodeLabel = ref(props.data?.label || 'Function')
const editorContainer = ref(null)
let editorView = null

// Console state
const consoleLogs = ref([
  '> Processing event: { "id": 123, "user": "test" }',
  '> { "id": 123, "user": "test", "processed": true, "timestamp": "2023-10-27T10:00:00.000Z" }'
])

const showMenu = ref(false)
const isEditing = ref(false)
const editableLabel = ref('')
const titleInput = ref(null)

function openEditor() {
  showEditor.value = true
  // Prevent body scroll when modal is open
  document.body.style.overflow = 'hidden'
  nextTick(() => {
    initCodeMirror()
  })
}

function closeEditor() {
  if (editorView) {
    editorView.destroy()
    editorView = null
  }
  showEditor.value = false
  // Restore body scroll when modal is closed
  document.body.style.overflow = ''
}

function initCodeMirror() {
  if (!editorContainer.value) return

  // Check if system prefers dark mode
  const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
  const selectedTheme = prefersDark ? darkTheme : lightTheme

  const state = EditorState.create({
    doc: jsCode.value,
    extensions: [
      basicSetup,
      javascript(),
      selectedTheme,
      EditorView.updateListener.of((update) => {
        if (update.docChanged) {
          jsCode.value = update.state.doc.toString()
        }
      })
    ]
  })

  editorView = new EditorView({
    state,
    parent: editorContainer.value
  })
}

// Menu functions
function toggleMenu() {
  showMenu.value = !showMenu.value
}

function startEditing() {
  isEditing.value = true
  editableLabel.value = nodeLabel.value
  showMenu.value = false
  nextTick(() => {
    if (titleInput.value) {
      titleInput.value.focus()
      titleInput.value.select()
    }
  })
}

function stopEditing() {
  if (editableLabel.value.trim()) {
    nodeLabel.value = editableLabel.value.trim()
    updateNodeData()
  }
  isEditing.value = false
}

function cancelEditing() {
  isEditing.value = false
  editableLabel.value = ''
}

function updateNodeData() {
  const newData = {
    ...props.data,
    label: nodeLabel.value
  }

  emit('update:data', newData)

  // CustomEvent dispatch
  const updateEvent = new CustomEvent('node:update', {
    detail: { id: props.id, data: newData }
  })
  window.dispatchEvent(updateEvent)
}

function openDocumentation() {
  // TODO: Open documentation
  console.log('Opening documentation for function node')
  showMenu.value = false
}

function deleteNode() {
  showMenu.value = false
  removeNodes([props.id])
}

function clearConsole() {
  consoleLogs.value = []
}

function saveFunction() {
  const newData = {
    ...props.data,
    jsCode: jsCode.value,
    label: nodeLabel.value
  }

  emit('update:data', newData)

  // CustomEvent dispatch
  const updateEvent = new CustomEvent('node:update', {
    detail: { id: props.id, data: newData }
  })
  window.dispatchEvent(updateEvent)

  closeEditor()
}

onUnmounted(() => {
  if (editorView) {
    editorView.destroy()
  }
  // Restore body scroll if component is unmounted while modal is open
  document.body.style.overflow = ''
})
</script>

<style scoped>
.function-node {
  position: relative;
  background: white;
  border: 2px solid #10b981;
  border-radius: 8px;
  min-width: 200px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 8px;
}

.function-node-header {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  padding: 12px;
  margin-bottom: 8px;
}

.function-node-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #10b981;
  color: white;
  border-radius: 4px;
  padding: 6px;
  margin-right: 12px;
}

.function-node-title {
  font-weight: bold;
  font-size: 14px;
  cursor: pointer;
  flex: 1;
  min-width: 0;
  color: #333;
}

.function-node-title:hover {
  color: #10b981;
  text-decoration: underline dotted;
}

.title-input {
  font-weight: bold;
  font-size: 14px;
  border: 1px solid #10b981;
  border-radius: 4px;
  padding: 2px 4px;
  width: 120px;
  outline: none;
  flex: 1;
  min-width: 0;
  color: #333;
  background: white;
}

.function-node-content {
  font-size: 12px;
  padding: 12px 8px;
  cursor: pointer;
  color: #666;
  text-align: center;
  border-radius: 4px;
  transition: all 0.2s;
}

.function-node-content:hover {
  background-color: #f8f9fa;
  color: #10b981;
}

.edit-hint {
  font-style: italic;
}

/* Menu styles */
.node-menu {
  position: relative;
  margin-left: auto;
}

.menu-trigger {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.menu-trigger:hover,
.menu-trigger.active {
  background-color: #e0e0e0;
  color: #333;
}

.menu-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 140px;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  font-size: 13px;
  color: #333;
  transition: background-color 0.2s ease;
}

.menu-item:hover {
  background-color: #f5f5f5;
}

.menu-item.delete {
  color: #dc3545;
}

.menu-item.delete:hover {
  background-color: #ffeaea;
}

/* Function Editor Modal Styles */
.editor-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background: rgba(17, 24, 39, 0.75) !important;
  backdrop-filter: blur(4px) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 999999 !important;
  padding: 1rem !important;
  margin: 0 !important;
  transform: none !important;
  pointer-events: auto !important;
  box-sizing: border-box !important;
}

.editor-modal {
  background: #ffffff !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
  width: 100% !important;
  max-width: 96rem !important;
  min-width: 80vw !important;
  display: flex !important;
  flex-direction: column !important;
  height: 80vh !important;
  min-height: 600px !important;
  overflow: hidden !important;
  position: relative !important;
  transform: none !important;
  margin: 0 auto !important;
}

@media (prefers-color-scheme: dark) {
  .editor-modal {
    background: #1f2937 !important;
  }
}

/* Header */
.editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #e5e7eb;
}

@media (prefers-color-scheme: dark) {
  .editor-header {
    border-bottom: 1px solid #374151;
  }
}

.editor-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

@media (prefers-color-scheme: dark) {
  .editor-title {
    color: #f9fafb;
  }
}

.close-button {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.close-button:hover {
  color: #111827;
  background: #f3f4f6;
}

@media (prefers-color-scheme: dark) {
  .close-button {
    color: #9ca3af;
  }

  .close-button:hover {
    color: #f9fafb;
    background: #374151;
  }
}

/* Main Content */
.editor-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.editor-section {
  flex: 2;
  display: flex;
  flex-direction: column;
}

.codemirror-container {
  flex: 1;
  background: #ffffff;
  border: none;
  overflow: hidden;
}

.codemirror-container .cm-editor {
  height: 100%;
}

.codemirror-container .cm-focused {
  outline: none;
}

/* Console Section */
.console-section {
  flex: 1;
  border-left: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
}

@media (prefers-color-scheme: dark) {
  .console-section {
    border-left: 1px solid #374151;
  }
}

.console-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.375rem 0.75rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

@media (prefers-color-scheme: dark) {
  .console-header {
    border-bottom: 1px solid #374151;
    background: #1f2937;
  }
}

.console-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

@media (prefers-color-scheme: dark) {
  .console-title {
    color: #f9fafb;
  }
}

.console-clear-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.console-clear-btn:hover {
  color: #111827;
  background: #e5e7eb;
}

@media (prefers-color-scheme: dark) {
  .console-clear-btn {
    color: #9ca3af;
  }

  .console-clear-btn:hover {
    color: #f9fafb;
    background: #374151;
  }
}

.console-content {
  flex: 1;
  padding: 0.75rem;
  background: #f9fafb;
  overflow-y: auto;
  font-family: 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', monospace;
  font-size: 0.75rem;
  color: #6b7280;
}

@media (prefers-color-scheme: dark) {
  .console-content {
    background: #1f2937;
    color: #9ca3af;
  }
}

.console-log {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.25rem;
}

.console-arrow {
  color: #3b82f6;
  margin-right: 0.5rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.console-text {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}

.console-empty {
  color: #9ca3af;
  font-style: italic;
  text-align: center;
  padding: 2rem;
}

/* Footer */
.editor-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  padding: 0.5rem;
  border-top: 1px solid #e5e7eb;
}

@media (prefers-color-scheme: dark) {
  .editor-footer {
    border-top: 1px solid #374151;
  }
}

.footer-btn {
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  border: none;
}

.footer-btn-secondary {
  color: #6b7280;
  background: #f3f4f6;
}

.footer-btn-secondary:hover {
  background: #e5e7eb;
  color: #111827;
}

@media (prefers-color-scheme: dark) {
  .footer-btn-secondary {
    color: #9ca3af;
    background: #374151;
  }

  .footer-btn-secondary:hover {
    background: #4b5563;
    color: #f9fafb;
  }
}

.footer-btn-primary {
  background: #4f46e5;
  color: #ffffff;
}

.footer-btn-primary:hover {
  background: #4338ca;
}
</style>
