<template>
  <div class="excel-node" :class="{ selected: selected }">
    <div class="excel-node-header">
      <div class="excel-node-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
          <polyline points="14,2 14,8 20,8"/>
          <path d="M16 13H8"/>
          <path d="M16 17H8"/>
          <path d="M10 9H8"/>
        </svg>
      </div>
      <div class="excel-node-title" v-if="!isEditing" @click="startEditing">
        {{ data.label || 'Excel' }}
      </div>
      <input v-else ref="titleInput" v-model="editableLabel" class="title-input" @blur="stopEditing"
        @keyup.enter="stopEditing" @keyup.esc="cancelEditing" type="text" />

      <div class="node-menu" @click.stop>
        <button class="menu-trigger" @click="toggleMenu" :class="{ active: showMenu }">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="1"></circle>
            <circle cx="12" cy="5" r="1"></circle>
            <circle cx="12" cy="19" r="1"></circle>
          </svg>
        </button>

        <div v-if="showMenu" class="menu-dropdown" @click.stop>
          <button class="menu-item" @click="openDocumentation">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14,2 14,8 20,8"></polyline>
            </svg>
            Documentation
          </button>

          <button class="menu-item delete" @click="deleteNode">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="3,6 5,6 21,6"></polyline>
              <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
            </svg>
            Delete
          </button>
        </div>
      </div>
    </div>
    
    <div class="excel-node-content">
      <!-- File Upload Section -->
      <div class="parameter-group">
        <label class="parameter-label">Excel/CSV File</label>
        <div class="file-upload-area" @click="triggerFileInput" @dragover.prevent @drop.prevent="handleFileDrop">
          <input ref="fileInput" type="file" accept=".xlsx,.xls,.csv" @change="handleFileSelect" style="display: none;">
          <div v-if="!selectedFile" class="upload-placeholder">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
              <polyline points="7,10 12,15 17,10"/>
              <line x1="12" y1="15" x2="12" y2="3"/>
            </svg>
            <span>Click or drag Excel/CSV file here</span>
          </div>
          <div v-else class="file-info">
            <svg v-if="isCSVFile" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
              <polyline points="14,2 14,8 20,8"/>
              <path d="M16 13H8"/>
              <path d="M16 17H8"/>
              <path d="M10 9H8"/>
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
              <polyline points="14,2 14,8 20,8"/>
            </svg>
            <span>{{ selectedFile.name }}</span>
            <span class="file-type-badge">{{ getFileType() }}</span>
            <button @click.stop="removeFile" class="remove-file-btn">×</button>
          </div>
        </div>
      </div>

      <!-- Column Selection Section -->
      <div v-if="excelColumns.length > 0" class="parameter-group">
        <label class="parameter-label">Select Columns</label>
        <div class="columns-container">
          <div v-for="column in excelColumns" :key="column" class="column-item">
            <label class="column-checkbox">
              <input 
                type="checkbox" 
                :value="column" 
                v-model="selectedColumns"
                @change="updateSelectedColumns"
              >
              <span class="checkmark"></span>
              <span class="column-name">{{ column }}</span>
            </label>
          </div>
        </div>
      </div>

      <!-- Selected Columns Info -->
      <div v-if="selectedColumns.length > 0" class="parameter-group">
        <label class="parameter-label">Selected ({{ selectedColumns.length }})</label>
        <div class="selected-columns">
          <span v-for="column in selectedColumns" :key="column" class="selected-column-tag">
            {{ column }}
          </span>
        </div>
      </div>

      <div class="parameter-group">
        <label class="parameter-label">Operation</label>
        <select v-model="selectedOperation" @change="updateOperation" class="operation-dropdown">
          <option value="get_data">Get Data</option>
          <option value="get_data">Delete</option>
          <option value="get_data">Insert</option>
        </select>
      </div>

      <div class="parameter-group">
        <label class="parameter-label">Data Limit</label>
        <input
          :value="dataLimit"
          @input="handleDataLimitInput"
          @blur="validateDataLimit"
          type="number"
          min="3"
          max="10000"
          step="1"
          placeholder="Enter limit (e.g., 100)"
          class="limit-input"
        >
        <span class="limit-hint">Minimum 3, Maximum 10,000 rows to process</span>
      </div>

      <div class="parameter-group">
        <label class="save-data-checkbox">
          <input
            type="checkbox"
            v-model="saveData"
            @change="updateSaveData"
          >
          <span class="checkmark"></span>
          <span class="checkbox-label">Save Data</span>
        </label>
        <span class="save-data-hint">Save processed data to backend storage</span>
      </div>
    </div>
    
    <NodeResizer v-if="selected" :min-width="280" :min-height="200" :is-visible="selected" />
    <Handle id="target" type="target" position="left" />
    <Handle id="source" type="source" position="right" />
  </div>
</template>

<script setup>
import { Handle, useVueFlow } from '@vue-flow/core'
import { NodeResizer } from '@vue-flow/node-resizer'
import '@vue-flow/node-resizer/dist/style.css'
import { ref, nextTick, computed } from 'vue'


const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  data: {
    type: Object,
    required: true,
  },
  selected: {
    type: Boolean,
    default: false,
  },
  position: {
    type: Object,
    default: () => ({ x: 0, y: 0 })
  },
})

const emit = defineEmits(['update:data'])

const { removeNodes } = useVueFlow()

const isEditing = ref(false)
const editableLabel = ref('')
const titleInput = ref(null)
const showMenu = ref(false)
const fileInput = ref(null)
const selectedFile = ref(null)
const excelColumns = ref([])
const selectedColumns = ref([])
const selectedOperation = ref('get_data')
const dataLimit = ref(100)
const saveData = ref(false)

// Computed properties
const isCSVFile = computed(() => {
  return selectedFile.value && selectedFile.value.name.toLowerCase().endsWith('.csv')
})

// Initialize from existing data
if (props.data.selectedColumns) {
  selectedColumns.value = [...props.data.selectedColumns]
}
if (props.data.excelColumns) {
  excelColumns.value = [...props.data.excelColumns]
}
if (props.data.operation) {
  selectedOperation.value = props.data.operation
}
if (props.data.dataLimit) {
  dataLimit.value = props.data.dataLimit
}
if (props.data.saveData !== undefined) {
  saveData.value = props.data.saveData
}

function startEditing() {
  editableLabel.value = props.data.label || 'Excel'
  isEditing.value = true

  nextTick(() => {
    titleInput.value.focus()
  })
}

function stopEditing() {
  if (isEditing.value) {
    isEditing.value = false
    if (editableLabel.value.trim()) {
      const updatedData = { ...props.data, label: editableLabel.value.trim() }
      emit('update:data', updatedData)
      const updateEvent = new CustomEvent('node:update', {
        detail: { id: props.id, data: updatedData }
      })
      window.dispatchEvent(updateEvent)
    }
  }
}

function cancelEditing() {
  isEditing.value = false
}

function toggleMenu() {
  showMenu.value = !showMenu.value
}

function openDocumentation() {
  showMenu.value = false
  alert('Documentation for Excel Node')
}

function deleteNode() {
  showMenu.value = false
  removeNodes([props.id])
}

function triggerFileInput() {
  fileInput.value.click()
}

function handleFileSelect(event) {
  const file = event.target.files[0]
  if (file) {
    processFile(file)
  }
}

function handleFileDrop(event) {
  const file = event.dataTransfer.files[0]
  if (file && (file.name.endsWith('.xlsx') || file.name.endsWith('.xls') || file.name.endsWith('.csv'))) {
    processFile(file)
  }
}

function getFileType() {
  if (!selectedFile.value) return ''
  const fileName = selectedFile.value.name.toLowerCase()
  if (fileName.endsWith('.csv')) return 'CSV'
  if (fileName.endsWith('.xlsx')) return 'XLSX'
  if (fileName.endsWith('.xls')) return 'XLS'
  return 'Unknown'
}

function processFile(file) {
  selectedFile.value = file

  const fileName = file.name.toLowerCase()
  if (fileName.endsWith('.csv')) {
    processCSVFile(file)
  } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
    processExcelFile(file)
  } else {
    alert('Unsupported file format. Please select Excel (.xlsx, .xls) or CSV (.csv) files.')
  }
}

function processCSVFile(file) {
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const csvText = e.target.result
      const jsonData = parseCSV(csvText)

      if (jsonData.length > 0) {
        // Get column headers from first row
        const columns = jsonData[0].filter(col => col !== undefined && col !== null && col !== '')
        excelColumns.value = columns
        selectedColumns.value = [] // Reset selection

        // Update node data
        updateNodeData({
          fileName: file.name,
          excelColumns: columns,
          selectedColumns: [],
          excelData: jsonData,
          fileType: 'csv',
          operation: selectedOperation.value,
          dataLimit: dataLimit.value,
          saveData: saveData.value
        })
      }
    } catch (error) {
      console.error('Error processing CSV file:', error)
      alert('Error processing CSV file. Please check the file format.')
    }
  }
  reader.readAsText(file)
}

async function processExcelFile(file) {
  const reader = new FileReader()
  reader.onload = async (e) => {
    try {
      // Dynamic import for better compatibility
      const XLSX = await import('xlsx')
      const data = new Uint8Array(e.target.result)
      const workbook = XLSX.read(data, { type: 'array' })
      const firstSheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[firstSheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

      if (jsonData.length > 0) {
        // Get column headers from first row
        const columns = jsonData[0].filter(col => col !== undefined && col !== null && col !== '')
        excelColumns.value = columns
        selectedColumns.value = [] // Reset selection

        // Update node data
        updateNodeData({
          fileName: file.name,
          excelColumns: columns,
          selectedColumns: [],
          excelData: jsonData,
          fileType: 'excel',
          operation: selectedOperation.value,
          dataLimit: dataLimit.value,
          saveData: saveData.value
        })
      }
    } catch (error) {
      console.error('Error processing Excel file:', error)
      alert('Error processing Excel file. Please check the file format.')
    }
  }
  reader.readAsArrayBuffer(file)
}

function parseCSV(csvText) {
  const lines = csvText.split('\n')
  const result = []

  for (let line of lines) {
    if (line.trim() === '') continue

    // Simple CSV parsing (handles basic cases)
    const row = []
    let current = ''
    let inQuotes = false

    for (let i = 0; i < line.length; i++) {
      const char = line[i]

      if (char === '"') {
        inQuotes = !inQuotes
      } else if (char === ',' && !inQuotes) {
        row.push(current.trim())
        current = ''
      } else {
        current += char
      }
    }

    // Add the last field
    row.push(current.trim())
    result.push(row)
  }

  return result
}

function removeFile() {
  selectedFile.value = null
  excelColumns.value = []
  selectedColumns.value = []
  fileInput.value.value = ''
  
  updateNodeData({
    fileName: null,
    excelColumns: [],
    selectedColumns: [],
    excelData: null,
    fileType: null,
    operation: selectedOperation.value,
    dataLimit: dataLimit.value,
    saveData: saveData.value
  })
}

function updateSelectedColumns() {
  updateNodeData({
    selectedColumns: [...selectedColumns.value]
  })
}

function updateOperation() {
  updateNodeData({
    operation: selectedOperation.value
  })
}

function handleDataLimitInput(event) {
  dataLimit.value = event.target.value
}

function validateDataLimit() {
  let numValue = typeof dataLimit.value === 'string' ? parseInt(dataLimit.value) : dataLimit.value

  if (isNaN(numValue) || numValue < 3) {
    numValue = 3
  }
  if (numValue > 10000) {
    numValue = 10000
  }

  numValue = Math.floor(numValue)

  dataLimit.value = numValue

  updateNodeData({
    dataLimit: numValue
  })
}

function updateSaveData() {
  updateNodeData({
    saveData: saveData.value
  })
}

function updateNodeData(newData) {
  const updatedData = { ...props.data, ...newData }
  emit('update:data', updatedData)
  const updateEvent = new CustomEvent('node:update', {
    detail: { id: props.id, data: updatedData }
  })
  window.dispatchEvent(updateEvent)
}
</script>

<style scoped>
.excel-node {
  padding: 10px;
  border-radius: 8px;
  background-color: #f0f9ff;
  border: 2px solid #0ea5e9;
  width: 280px;
  color: #333;
  font-family: sans-serif;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.excel-node.selected {
  box-shadow: 0 0 0 2px #ff6b6b;
  border-color: #ff6b6b;
}

.excel-node-header {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 8px;
  margin-bottom: 8px;
}

.excel-node-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #0ea5e9;
  color: white;
  border-radius: 4px;
  padding: 4px;
  margin-right: 8px;
}

.excel-node-title {
  font-weight: bold;
  font-size: 14px;
  cursor: pointer;
}

.excel-node-title:hover {
  color: #0ea5e9;
  text-decoration: underline dotted;
}

.title-input {
  font-weight: bold;
  font-size: 14px;
  border: 1px solid #0ea5e9;
  border-radius: 4px;
  padding: 2px 4px;
  width: 100px;
  outline: none;
}

.excel-node-content {
  font-size: 12px;
  padding: 4px 0;
}

.parameter-group {
  margin-bottom: 8px;
}

.parameter-label {
  display: block;
  font-size: 11px;
  font-weight: 600;
  color: #4b5563;
  margin-bottom: 4px;
}

.file-upload-area {
  border: 2px dashed #cbd5e1;
  border-radius: 6px;
  padding: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: #f8fafc;
}

.file-upload-area:hover {
  border-color: #0ea5e9;
  background-color: #f0f9ff;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #64748b;
}

.upload-placeholder svg {
  color: #94a3b8;
}

.upload-placeholder span {
  font-size: 11px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #0ea5e9;
  font-size: 11px;
}

.file-type-badge {
  background-color: #0ea5e9;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 9px;
  font-weight: 600;
  text-transform: uppercase;
}

.operation-dropdown {
  width: 100%;
  padding: 4px 6px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background-color: #ffffff;
  color: #374151;
  font-size: 11px;
  outline: none;
  transition: border-color 0.2s ease;
}

.operation-dropdown:focus {
  border-color: #0ea5e9;
  box-shadow: 0 0 0 1px #0ea5e9;
}

.limit-input {
  width: 100%;
  padding: 4px 6px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background-color: #ffffff;
  color: #374151;
  font-size: 11px;
  outline: none;
  transition: border-color 0.2s ease;
}

.limit-input:focus {
  border-color: #0ea5e9;
  box-shadow: 0 0 0 1px #0ea5e9;
}

.limit-input::placeholder {
  color: #9ca3af;
}

.limit-hint {
  font-size: 10px;
  color: #6b7280;
  margin-top: 2px;
  display: block;
}

.save-data-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 11px;
  color: #374151;
  padding: 4px 0;
  transition: all 0.2s ease;
}

.save-data-checkbox:hover {
  color: #0ea5e9;
}

.save-data-checkbox input[type="checkbox"] {
  display: none;
}

.save-data-checkbox .checkmark {
  width: 16px;
  height: 16px;
  border: 1px solid #d1d5db;
  border-radius: 3px;
  background-color: #ffffff;
  position: relative;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.save-data-checkbox:hover .checkmark {
  border-color: #0ea5e9;
}

.save-data-checkbox input[type="checkbox"]:checked + .checkmark {
  background-color: #0ea5e9;
  border-color: #0ea5e9;
}

.save-data-checkbox input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 10px;
  font-weight: bold;
}

.checkbox-label {
  font-weight: 500;
  user-select: none;
}

.save-data-hint {
  font-size: 10px;
  color: #6b7280;
  margin-top: 2px;
  display: block;
  padding-left: 24px;
}

@media (max-width: 768px) {
  .excel-node {
    width: 260px;
    padding: 8px;
  }

  .parameter-group {
    margin-bottom: 6px;
  }

  .operation-dropdown,
  .limit-input {
    padding: 3px 5px;
    font-size: 10px;
  }

  .limit-input {
    min-width: 80px;
  }

  .save-data-checkbox {
    font-size: 10px;
  }

  .save-data-checkbox .checkmark {
    width: 14px;
    height: 14px;
  }
}

@media (max-width: 480px) {
  .excel-node {
    width: 240px;
    padding: 6px;
  }

  .parameter-label {
    font-size: 10px;
    margin-bottom: 3px;
  }

  .operation-dropdown,
  .limit-input {
    padding: 3px 4px;
    font-size: 10px;
  }

  .limit-hint,
  .save-data-hint {
    font-size: 9px;
  }
}

.remove-file-btn {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.columns-container {
  max-height: 120px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 4px;
  background-color: white;
}

.column-item {
  padding: 2px 4px;
}

.column-checkbox {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 11px;
}

.column-checkbox input[type="checkbox"] {
  margin: 0;
}

.column-name {
  color: #374151;
}

.selected-columns {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.selected-column-tag {
  background-color: #0ea5e9;
  color: white;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
}

/* Menu styles */
.node-menu {
  position: relative;
  margin-left: auto;
}

.menu-trigger {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.menu-trigger:hover,
.menu-trigger.active {
  background-color: #e0e0e0;
  color: #333;
}

.menu-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 140px;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  font-size: 13px;
  color: #333;
  transition: background-color 0.2s ease;
}

.menu-item:hover {
  background-color: #f5f5f5;
}

.menu-item.delete {
  color: #dc3545;
}

.menu-item.delete:hover {
  background-color: #ffeaea;
}
</style>
