<template>
  <div class="json-editor-panel" :class="{ 'visible': isVisible }" :style="editorStyle" ref="editorPanel"
    v-show="isVisible">
    <div class="json-editor-header" @mousedown="startDrag" @touchstart="startDrag">
      <div class="drag-handle">
        <span></span>
        <span></span>
        <span></span>
      </div>
      <div class="resize-handle" @mousedown="startResize" @touchstart="startResize">
        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M9 9h6"/>
          <path d="M15 9v6"/>
        </svg>
      </div>
    </div>
    <div class="json-editor-content">
      <div class="split-editor">
        <div class="editor-section request-section">
          <div class="section-header">
            <span>Input</span>
            <button class="run-button" @click="executeRequest">
              <span class="run-icon">▶</span> Run
            </button>
          </div>
          <div class="editor-container" :style="{ height: requestEditorHeight }">
            <div ref="requestMonacoContainer" class="monaco-editor-container"></div>
          </div>
        </div>

        <div class="editor-section output-section">
          <div class="section-header">
            <span>Output</span>
            <button class="copy-button" @click="copyOutput" :disabled="!outputJsonData || Object.keys(outputJsonData).length === 0">
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect width="14" height="14" x="8" y="8" rx="2" ry="2"/>
                <path d="m4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/>
              </svg>
              Copy
            </button>
          </div>
          <div class="editor-container" :style="{ height: outputEditorHeight }">
            <div ref="outputMonacoContainer" class="monaco-editor-container"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed, onMounted, nextTick, onBeforeUnmount, reactive } from 'vue';
import * as monaco from 'monaco-editor';
import { useSimulateStore } from '@/store/submodules/simulate';

const store = useSimulateStore();
const route = useRoute();
const toast = inject('toast')

import { editNodeAndEdge } from '../utils/graphUtils'

// Props
const props = defineProps({
  isVisible: {
    type: Boolean,
    default: false
  },
  initialJson: {
    type: Object,
    default: () => ({})
  },
  nodes: {
    type: Array,
    default: () => []
  },
  edges: {
    type: Array,
    default: () => []
  },
  simulateInfo: {
    type: Object,
    default: () => ({ id: "", name: "" })
  }
});

// Emits
const emit = defineEmits(['update:isVisible', 'applyJson']);

const requestJsonData = ref(props.initialJson || {});
const outputJsonData = ref({});
const editorPanel = ref(null);

async function loadDefaultRequest() {
  const simulateId = props.simulateInfo?.id || route.params.id;

  if (simulateId) {
    try {
      const response = await store.GetDefaultRequest(simulateId, '2');
      if (response) {
        let defaultData = {};
        if (response.value) {
          try {
            defaultData = typeof response.value === 'string'
              ? JSON.parse(response.value)
              : response.value;
          } catch (parseError) {
            console.error('Error parsing default request value:', parseError);
            toast.error('Error parsing default request value. Please check your JSON syntax.');
            defaultData = {};
          }
        }

        requestJsonData.value = defaultData;

        if (requestMonacoEditor) {
          const jsonString = JSON.stringify(defaultData, null, 2);
          requestMonacoEditor.setValue(jsonString);
          nextTick(() => {
            updateRequestHeight();
          });
        }
      } else {
        requestJsonData.value = {};
        toast.error('No default request found');
      }
    } catch (error) {
      toast.error('Failed to load default request. Please try again later.');
      console.log('Failed to load default request:', error);
    }
  } else {
    toast.error('No simulate ID found. Please try again later.');
    console.log('No simulate ID found');
  }
}

const requestMonacoContainer = ref(null);
const outputMonacoContainer = ref(null);
let requestMonacoEditor = null;
let outputMonacoEditor = null;

const requestEditorHeight = ref('40px');
const outputEditorHeight = ref('40px');

function updateRequestHeight() {
  updateRequestEditorHeight();
}

function updateOutputHeight() {
  updateOutputEditorHeight();
}

function updateRequestEditorHeight() {
  if (requestMonacoEditor && requestMonacoContainer.value) {
    const model = requestMonacoEditor.getModel();
    if (model) {
      const lineCount = model.getLineCount();
      const lineHeight = requestMonacoEditor.getOption(monaco.editor.EditorOption.lineHeight);
      const contentHeight = lineCount * lineHeight + 10;
      const height = Math.max(40, Math.min(contentHeight, 300));

      requestEditorHeight.value = `${height}px`;
      requestMonacoContainer.value.style.height = `${height}px`;

      requestMonacoEditor.layout({
        width: requestMonacoContainer.value.offsetWidth,
        height: height
      });

      updateOutputEditorHeight();
    }
  }
}

function updateOutputEditorHeight() {
  if (outputMonacoEditor && outputMonacoContainer.value) {
    const model = outputMonacoEditor.getModel();
    let height;

    if (model) {
      const lineCount = model.getLineCount();
      const lineHeight = outputMonacoEditor.getOption(monaco.editor.EditorOption.lineHeight);
      const contentHeight = lineCount * lineHeight + 10;
      height = Math.max(40, Math.min(contentHeight, 300));
    } else {
      height = 40;
    }

    outputEditorHeight.value = `${height}px`;
    outputMonacoContainer.value.style.height = `${height}px`;

    outputMonacoEditor.layout({
      width: outputMonacoContainer.value.offsetWidth,
      height: height
    });
  }
}

function initRequestMonacoEditor() {
  if (!requestMonacoContainer.value || requestMonacoEditor) {
    return;
  }

  const requestData = JSON.stringify(requestJsonData.value, null, 2);

  requestMonacoEditor = monaco.editor.create(requestMonacoContainer.value, {
    value: requestData,
    language: 'json',
    theme: 'vs-dark',
    lineNumbers: 'on',
    automaticLayout: false,
    minimap: { enabled: false },
    scrollBeyondLastLine: false,
    wordWrap: 'on',
    formatOnPaste: true,
    formatOnType: true,
    readOnly: false,
    fontSize: 13,
    lineHeight: 20,
    padding: { top: 5, bottom: 5 },
    scrollbar: {
      vertical: 'auto',
      horizontal: 'auto',
      verticalScrollbarSize: 8,
      horizontalScrollbarSize: 8
    },
    overviewRulerLanes: 0,
    hideCursorInOverviewRuler: true,
    overviewRulerBorder: false
  });

  requestMonacoEditor.onDidChangeModelContent(() => {
    try {
      const value = requestMonacoEditor.getValue();
      const parsed = JSON.parse(value);
      requestJsonData.value = parsed;
    } catch (e) {
    }

    setTimeout(() => {
      updateRequestEditorHeight();
    }, 100);
  });

  setTimeout(() => {
    updateRequestEditorHeight();
  }, 50);
}

function initOutputMonacoEditor() {
  if (!outputMonacoContainer.value || outputMonacoEditor) {
    return;
  }

  const outputData = JSON.stringify(outputJsonData.value, null, 2);

  outputMonacoEditor = monaco.editor.create(outputMonacoContainer.value, {
    value: outputData,
    language: 'json',
    theme: 'vs-dark',
    lineNumbers: 'on',
    automaticLayout: false,
    minimap: { enabled: false },
    scrollBeyondLastLine: false,
    wordWrap: 'on',
    readOnly: true,
    fontSize: 13,
    lineHeight: 20,
    padding: { top: 5, bottom: 5 },
    scrollbar: {
      vertical: 'auto',
      horizontal: 'auto',
      verticalScrollbarSize: 8,
      horizontalScrollbarSize: 8
    },
    overviewRulerLanes: 0,
    hideCursorInOverviewRuler: true,
    overviewRulerBorder: false
  });

  setTimeout(() => {
    updateOutputEditorHeight();
  }, 50);
}

const position = ref({ x: 0, y: 0 });
const isDragging = ref(false);
const dragOffset = ref({ x: 0, y: 0 });

const isResizing = ref(false);
const panelSize = ref({ width: 1200, height: 'auto' });
const resizeStartPos = ref({ x: 0, y: 0 });
const resizeStartSize = ref({ width: 0, height: 0 });

function startDrag(event) {
  const clientX = event.clientX || (event.touches && event.touches[0].clientX);
  const clientY = event.clientY || (event.touches && event.touches[0].clientY);

  if (clientX === undefined || clientY === undefined) return;

  isDragging.value = true;

  const rect = editorPanel.value.getBoundingClientRect();

  dragOffset.value = {
    x: clientX - rect.left,
    y: clientY - rect.top
  };

  document.addEventListener('mousemove', onDrag);
  document.addEventListener('touchmove', onDrag, { passive: false });
  document.addEventListener('mouseup', stopDrag);
  document.addEventListener('touchend', stopDrag);

  event.preventDefault();
}

function onDrag(event) {
  if (!isDragging.value) return;

  const clientX = event.clientX || (event.touches && event.touches[0].clientX);
  const clientY = event.clientY || (event.touches && event.touches[0].clientY);

  if (clientX === undefined || clientY === undefined) return;

  if (event.touches) {
    event.preventDefault();
  }

  position.value = {
    x: clientX - dragOffset.value.x,
    y: clientY - dragOffset.value.y
  };

  const rect = editorPanel.value.getBoundingClientRect();
  const windowWidth = window.innerWidth;
  const windowHeight = window.innerHeight;

  if (position.value.x + rect.width > windowWidth) {
    position.value.x = windowWidth - rect.width;
  }

  if (position.value.x < 0) {
    position.value.x = 0;
  }

  if (position.value.y + rect.height > windowHeight) {
    position.value.y = windowHeight - rect.height;
  }

  if (position.value.y < 0) {
    position.value.y = 0;
  }
}

function stopDrag() {
  isDragging.value = false;

  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('touchmove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
  document.removeEventListener('touchend', stopDrag);
}

function startResize(event) {
  const clientX = event.clientX || (event.touches && event.touches[0].clientX);

  if (clientX === undefined) return;

  isResizing.value = true;

  resizeStartPos.value = { x: clientX, y: 0 };
  resizeStartSize.value = {
    width: panelSize.value.width,
    height: 0
  };

  document.addEventListener('mousemove', onResize);
  document.addEventListener('touchmove', onResize, { passive: false });
  document.addEventListener('mouseup', stopResize);
  document.addEventListener('touchend', stopResize);

  event.preventDefault();
  event.stopPropagation();
}

function onResize(event) {
  if (!isResizing.value) return;

  const clientX = event.clientX || (event.touches && event.touches[0].clientX);

  if (clientX === undefined) return;

  if (event.touches) {
    event.preventDefault();
  }

  const deltaX = clientX - resizeStartPos.value.x;

  const newWidth = Math.max(600, Math.min(1800, resizeStartSize.value.width + deltaX));

  panelSize.value = { width: newWidth, height: 'auto' };

  nextTick(() => {
    if (requestMonacoEditor) {
      requestMonacoEditor.layout();
    }
    if (outputMonacoEditor) {
      outputMonacoEditor.layout();
    }
  });
}

function stopResize() {
  isResizing.value = false;

  document.removeEventListener('mousemove', onResize);
  document.removeEventListener('touchmove', onResize);
  document.removeEventListener('mouseup', stopResize);
  document.removeEventListener('touchend', stopResize);
}

function copyOutput() {
  try {
    let textToCopy = '';

    if (outputMonacoEditor) {
      textToCopy = outputMonacoEditor.getValue();
    } else {
      textToCopy = JSON.stringify(outputJsonData.value, null, 2);
    }

    navigator.clipboard.writeText(textToCopy).then(() => {
      toast.success('Output copied to clipboard!');
    }).catch(err => {
      toast.error('Failed to copy output: ' + err.message);
      const textArea = document.createElement('textarea');
      textArea.value = textToCopy;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
    });
  } catch (error) {
    toast.error('Failed to copy output: ' + error.message);
  }
}

const editorStyle = computed(() => {
  const baseStyle = {
    position: 'fixed',
    width: `${panelSize.value.width}px`,
    height: panelSize.value.height,
    zIndex: 1000
  };

  if (position.value.x === 0 && position.value.y === 0) {
    return {
      ...baseStyle,
      left: '50%',
      bottom: '5%',
      transform: 'translateX(-50%)'
    };
  } else {
    return {
      ...baseStyle,
      left: `${position.value.x}px`,
      top: `${position.value.y}px`,
      transform: 'none'
    };
  }
});

watch(() => props.initialJson, (newVal) => {
  requestJsonData.value = newVal || {};
  if (requestMonacoEditor) {
    const jsonString = JSON.stringify(newVal || {}, null, 2);
    requestMonacoEditor.setValue(jsonString);
  }
  nextTick(() => {
    updateRequestHeight();
  });
}, { immediate: true });

watch(() => outputJsonData.value, (newVal) => {
  if (outputMonacoEditor) {
    const jsonString = JSON.stringify(newVal, null, 2);
    outputMonacoEditor.setValue(jsonString);
  }
  nextTick(() => {
    updateOutputHeight();
  });
});

watch(() => props.isVisible, (newVal) => {
  if (newVal) {
    nextTick(async () => {
      if (!requestMonacoEditor) {
        initRequestMonacoEditor();
      }
      if (!outputMonacoEditor) {
        initOutputMonacoEditor();
      }

      await loadDefaultRequest();

      updateRequestHeight();
      updateOutputHeight();
    });
  }
}, { immediate: true });

const simulate_payload = reactive({
  id: "",
  name: "",
  context: {},
  content: {},  
  errors: {},
});

function executeRequest() {
  try {
    const editedData = editNodeAndEdge(props.nodes, props.edges)

    // Get data from Monaco editor if available, otherwise use reactive data
    let requestData = requestJsonData.value;
    if (requestMonacoEditor) {
      try {
        const editorValue = requestMonacoEditor.getValue();
        requestData = JSON.parse(editorValue);
      } catch (e) {
        toast.error('Invalid JSON format in request data. Please check your JSON syntax.');
        return;
      }
    }

    let contextData = {};
    if (typeof requestData === 'string') {
      try {
        contextData = JSON.parse(requestData);
      } catch (e) {
        toast.error('Invalid JSON format in request data. Please check your JSON syntax.');
        return; 
      }
    } else if (typeof requestData === 'object' && requestData !== null) {
      contextData = requestData;
    } else {
      contextData = { value: requestData };
    }

    simulate_payload.id = route.params.id
    simulate_payload.context = contextData;
    simulate_payload.content = {
      nodes: editedData.nodes,
      edges: editedData.edges
    };

    store
      .Simulate(simulate_payload)
      .then((res) => {
        outputJsonData.value = res.result;
        nextTick(() => {
          updateOutputHeight();
        });
      })
      .catch((err) => {
        toast.error('An error occurred while simulating the request.');
        console.log('Simulation Error:', err);
        outputJsonData.value = { error: err.message || 'Simulation failed' };
        nextTick(() => {
          updateOutputHeight();
        });
      });



  } catch (error) {
    toast.error('An error occurred while simulating the request.');
    console.log('Simulation Error:', error);
    outputJsonData.value = { error: error.message };
    nextTick(() => {
      updateOutputHeight();
    });
  }
}

onMounted(() => {
  nextTick(() => {
    setTimeout(() => {
      initRequestMonacoEditor();
      initOutputMonacoEditor();
    }, 100);
  });
});

function getRequestData() {
  return requestJsonData.value;
}

defineExpose({
  getRequestData,
  executeRequest
});

onBeforeUnmount(() => {
  if (requestMonacoEditor) {
    requestMonacoEditor.dispose();
    requestMonacoEditor = null;
  }
  if (outputMonacoEditor) {
    outputMonacoEditor.dispose();
    outputMonacoEditor = null;
  }

  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('touchmove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
  document.removeEventListener('touchend', stopDrag);

  document.removeEventListener('mousemove', onResize);
  document.removeEventListener('touchmove', onResize);
  document.removeEventListener('mouseup', stopResize);
  document.removeEventListener('touchend', stopResize);
});
</script>

<style scoped>
.json-editor-panel {
  background-color: #1e1e1e;
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-width: 600px;
  min-height: 300px;
}

.json-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 12px;
  background-color: #e4bc72;
  border-bottom: 1px solid #383838;
  cursor: move;
  user-select: none;
  color: white;
  position: relative;
  min-height: 32px;
}

.json-editor-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.drag-handle {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.drag-handle span {
  display: block;
  width: 20px;
  height: 2px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 1px;
}

.resize-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  cursor: ew-resize;
  border-radius: 3px;
  transition: background-color 0.2s ease;
  color: rgba(255, 255, 255, 0.7);
}

.resize-handle:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.resize-handle svg {
  width: 14px;
  height: 14px;
}

.json-editor-content {
  display: flex;
  flex-direction: column;
  background-color: #1e1e1e;
}

/* Bölünmüş editör stili */
.split-editor {
  display: flex;
  flex-direction: row;
  overflow: hidden;
}

.editor-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-right: 1px solid #383838;
}

.editor-section:last-child {
  border-right: none;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 10px;
  background-color: #2d2d2d;
  border-bottom: 1px solid #383838;
  color: white;
  font-size: 13px;
  min-height: 28px;
}

.run-button {
  display: flex;
  align-items: center;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 3px;
  padding: 3px 8px;
  font-size: 11px;
  cursor: pointer;
  transition: background-color 0.2s;
  height: 24px;
}

.run-button:hover {
  background-color: #45a049;
}

.run-icon {
  margin-right: 3px;
  font-size: 9px;
}

.copy-button {
  display: flex;
  align-items: center;
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 3px;
  padding: 3px 8px;
  font-size: 11px;
  cursor: pointer;
  transition: background-color 0.2s;
  height: 24px;
  gap: 3px;
}

.copy-button:hover:not(:disabled) {
  background-color: #1976D2;
}

.copy-button:disabled {
  background-color: #666;
  cursor: not-allowed;
  opacity: 0.6;
}

.editor-container {
  overflow: hidden;
  transition: height 0.3s ease;
}

.monaco-editor-container {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
}

@media (max-width: 992px) {
  .json-editor-panel {
    min-width: 300px;
    width: 90vw !important;
    height: 70vh !important;
  }

  .split-editor {
    flex-direction: column;
  }

  .editor-section {
    border-right: none;
    border-bottom: 1px solid #383838;
  }

  .editor-section:last-child {
    border-bottom: none;
  }

  .resize-handle {
    display: none;
  }
}
</style>