<script setup>
  import { ref, computed } from 'vue'
  import { Panel, useVueFlow } from '@vue-flow/core'
  import Icon from './icon.vue'
  import JsonEditorPanel from '@/components/flow-editor/json-editor/json-editor-panel.vue'

  const props = defineProps({
    simulateInfo: {
      type: Object,
      default: () => ({ id: "", name: "" })
    }
  })

  const flowKey = 'vue-flow--save-restore'
  const { toObject, fromObject, getNodes, getEdges } = useVueFlow()
  const isJsonEditorVisible = ref(false)
  const jsonEditorPanel = ref(null)
  // default json // TODO: get it from db later
  const defaultJsonContent = ref('{}')

  // Reactive nodes and edges
  const currentNodes = computed(() => getNodes.value || [])
  const currentEdges = computed(() => getEdges.value || [])

  function onSave() {
    localStorage.setItem(flowKey, JSON.stringify(toObject()))
  }

  function onRestore() {
    const flow = JSON.parse(localStorage.getItem(flowKey))

    if (flow) {
      fromObject(flow)
    }
  }
  
  function onAdd() {
    isJsonEditorVisible.value = !isJsonEditorVisible.value
  }
  
  function processJsonData(jsonData) {
    // TODO: localization
    try {
      console.log('İşlenen JSON verisi:', jsonData)
      
      localStorage.setItem('custom-config', JSON.stringify(jsonData))
      
      alert('Konfigürasyon başarıyla kaydedildi!')
      
    } catch (error) {
      console.error('JSON işleme hatası:', error)
      alert('Konfigürasyon işlenirken bir hata oluştu: ' + error.message)
    }
  }

  function getRequestData() {
    return jsonEditorPanel.value?.getRequestData() || null;
  }

  defineExpose({
    getRequestData,
    jsonEditorPanel
  });
</script>

<template>
  <Panel position="top-left">
    <div class="buttons">
      <button
        title="JSON Editörünü Aç/Kapat"
        @click="onAdd"
        :class="{ 'active': isJsonEditorVisible }"
      >
        <Icon name="play" />
      </button>
    </div>
  </Panel>
  
  <JsonEditorPanel
    ref="jsonEditorPanel"
    v-model:isVisible="isJsonEditorVisible"
    :initialJson="defaultJsonContent"
    :nodes="currentNodes"
    :edges="currentEdges"
    :simulateInfo="props.simulateInfo"
    @applyJson="processJsonData"
  />
</template>

<style scoped>
button.active {
  background-color: #2563eb !important;
}
</style>