import { useVueFlow } from '@vue-flow/core'
import { ref, watch } from 'vue'

/**
 * @returns {string} - A unique UUID.
 */
function getId() {
  return crypto.randomUUID()
}

/**
 * In a real world scenario you'd want to avoid creating refs in a global scope like this as they might not be cleaned up properly.
 * @type {{draggedType: Ref<string|null>, isDragOver: Ref<boolean>, isDragging: Ref<boolean>}}
 */
const state = {
  /**
   * The type of the node being dragged.
   */
  draggedType: ref(null),
  isDragOver: ref(false),
  isDragging: ref(false),
}

export default function useDragAndDrop() {
  const { draggedType, isDragOver, isDragging } = state

  const { addNodes, screenToFlowCoordinate, onNodesInitialized, updateNode, getNodes } = useVueFlow()

  watch(isDragging, (dragging) => {
    document.body.style.userSelect = dragging ? 'none' : ''
  })

  function onDragStart(event, type) {
    if (event.dataTransfer) {
      event.dataTransfer.setData('application/vueflow', type)
      event.dataTransfer.effectAllowed = 'move'
    }

    draggedType.value = type
    isDragging.value = true

    document.addEventListener('drop', onDragEnd)
  }

  /**
   * Handles the drag over event.
   *
   * @param {DragEvent} event
   */
  function onDragOver(event) {
    event.preventDefault()

    if (draggedType.value) {
      isDragOver.value = true

      if (event.dataTransfer) {
        event.dataTransfer.dropEffect = 'move'
      }
    }
  }

  function onDragLeave() {
    isDragOver.value = false
  }

  function onDragEnd() {
    isDragging.value = false
    isDragOver.value = false
    draggedType.value = null
    document.removeEventListener('drop', onDragEnd)
  }

  /**
   * Handles the drop event.
   *
   * @param {DragEvent} event
   */
  function onDrop(event) {
    event.preventDefault()
    if (!isDragging.value) return

    // Request node control only one
    if (draggedType.value === 'startNode') {
      const existingNodes = getNodes.value
      const hasRequestNode = existingNodes.some(node => node.type === 'startNode')

      if (hasRequestNode) {
        // TODO: add localization here
        alert('Only one start node is allowed!')
        onDragEnd()
        return
      }
    }

    const position = screenToFlowCoordinate({
      x: event.clientX,
      y: event.clientY,
    })

    const nodeId = getId()

    let nodeLabel = '';

    if (draggedType.value === 'startNode') {
      nodeLabel = 'Start';
    } else if (draggedType.value === 'endNode') {
      nodeLabel = 'End';
    } else if (draggedType.value === 'switchNode') {
      nodeLabel = 'Switch';
    } else if (draggedType.value === 'timeoutNode') {
      nodeLabel = 'Timeout';
    } else if (draggedType.value === 'functionNode') {
      nodeLabel = 'Function';
    } else if (draggedType.value === 'aiAgentNode') {
      nodeLabel = 'PARS AI Agent';
    } else if (draggedType.value === 'excelNode') {
      nodeLabel = 'Excel';
    } else {
      nodeLabel = draggedType.value.charAt(0).toUpperCase() + draggedType.value.slice(1);
    }

    let nodeData = {
      label: nodeLabel,
      description: `${nodeLabel} node`
    };

    // Switch node için özel data yapısı
    if (draggedType.value === 'switch') {
      const new_id = getId()
      nodeData = {
        ...nodeData,
        statements: [
          { id: new_id, condition: 'true', isDefault: false }
        ],
        mode: 'collect'
      };
    }

    if (draggedType.value === 'timeoutNode') {
      nodeData = {
        ...nodeData,
        timeout: 1 // default
      };
    }

    if (draggedType.value === 'functionNode') {
      nodeData = {
        ...nodeData,
        functionName: '',
        jsCode: ''
      };
    }

    if (draggedType.value === 'aiAgentNode') {
      nodeData = {
        ...nodeData,
        selectedAgent: ''
      };
    }

    if (draggedType.value === 'excelNode') {
      nodeData = {
        ...nodeData,
        fileName: null,
        excelColumns: [],
        selectedColumns: [],
        excelData: null,
        fileType: null,
        operation: 'get_data',
        dataLimit: 100,
        saveData: false
      };
    }

    const newNode = {
      id: nodeId,
      type: draggedType.value,
      position,
      data: nodeData,
    }

    console.log("Adding new node:", newNode)

    /**
     * Align node position after drop, so it's centered to the mouse
     *
     * We can hook into events even in a callback, and we can remove the event listener after it's been called.
     */
    const { off } = onNodesInitialized(() => {
      updateNode(nodeId, (node) => ({
        position: { x: node.position.x - node.dimensions.width / 2, y: node.position.y - node.dimensions.height / 2 },
      }))

      off()
    })

    addNodes(newNode)
    onDragEnd();
  }

  return {
    draggedType,
    isDragOver,
    isDragging,
    onDragStart,
    onDragLeave,
    onDragOver,
    onDrop,
  }
}