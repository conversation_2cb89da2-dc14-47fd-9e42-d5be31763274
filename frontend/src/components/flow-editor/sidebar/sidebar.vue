<template>
  <div class="sidebar-container" :class="{ 'collapsed': isCollapsed }">

    <aside>
      <div class="description">Components <span>(Drag and Drop)</span></div>
      <div class="nodes">
        <div class="node-item start-node-preview" :draggable="true" @dragstart="onDragStart($event, 'startNode')">
          <div class="node-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M5 12h14"></path>
              <path d="m12 5 7 7-7 7"></path>
            </svg>
          </div>
          <span class="node-text">Start</span>
        </div>
        <div class="node-item end-node-preview" :draggable="true" @dragstart="onDragStart($event, 'endNode')">
          <div class="node-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M19 12H5"></path>
              <path d="m12 19-7-7 7-7"></path>
            </svg>
          </div>
          <span class="node-text">End</span>
        </div>

        <div class="node-item switch-node-preview" :draggable="true" @dragstart="onDragStart($event, 'switchNode')">
          <div class="node-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 2 L20 12 L12 22 L4 12 Z" fill="currentColor" stroke="none"/>
              <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" stroke="white" stroke-width="1" fill="none"/>
              <circle cx="12" cy="17" r="0.8" fill="white"/>
            </svg>
          </div>
          <span class="node-text">Switch</span>
        </div>

        <div class="node-item timeout-node-preview" :draggable="true" @dragstart="onDragStart($event, 'timeoutNode')">
          <div class="node-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"/>
              <polyline points="12,6 12,12 16,14"/>
            </svg>
          </div>
          <span class="node-text">Timeout</span>
        </div>

        <div class="node-item function-node-preview" :draggable="true" @dragstart="onDragStart($event, 'functionNode')">
          <div class="node-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M3 3h18v18H3V3zm16.525 13.707c-.131-.821-.666-1.511-2.252-2.155-.552-.259-1.165-.438-1.349-.854-.068-.248-.078-.382-.034-.529.113-.484.687-.629 1.137-.495.293.09.563.315.732.676.775-.507.775-.507 1.316-.844-.203-.314-.304-.451-.439-.586-.473-.528-1.103-.798-2.126-.77l-.528.067c-.507.124-.991.395-1.283.754-.855.968-.608 2.655.427 3.354 1.023.765 2.521.933 2.712 1.653.18.878-.652 1.159-1.475 1.058-.607-.136-.945-.439-1.316-1.002l-1.372.788c.157.359.337.517.607.832 1.305 1.316 4.568 1.249 5.153-.754.021-.067.18-.528.056-1.237l.034.049zm-6.737-5.434h-1.686c0 1.453-.007 2.898-.007 4.354 0 .924.047 1.772-.104 2.033-.247.517-.886.451-1.175.359-.297-.146-.448-.349-.623-.641-.047-.078-.082-.146-.095-.146l-1.368.844c.229.473.563.879.994 1.137.641.383 1.502.507 2.404.305.588-.17 1.095-.519 1.358-1.059.384-.697.302-1.553.299-2.509.008-1.541 0-3.083 0-4.635l.003-.042z"/>
            </svg>
          </div>
          <span class="node-text">Function</span>
        </div>

        <div class="node-item ai-agent-node-preview" :draggable="true" @dragstart="onDragStart($event, 'aiAgentNode')">
          <div class="node-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="3"/>
              <path d="M12 1v6m0 6v6"/>
              <path d="m21 12-6-3-6 3-6-3"/>
              <path d="m21 12-6 3-6-3-6 3"/>
              <path d="M12 1 9 3l3 3 3-3z"/>
              <path d="M12 17l-3 3 3 3 3-3z"/>
              <path d="M5 12l-3-3 3-3 3 3z"/>
              <path d="M19 12l-3-3 3-3 3 3z"/>
            </svg>
          </div>
          <span class="node-text">PARS AI Agent</span>
        </div>

        <div class="node-item excel-node-preview" :draggable="true" @dragstart="onDragStart($event, 'excelNode')">
          <div class="node-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
              <polyline points="14,2 14,8 20,8"/>
              <path d="M16 13H8"/>
              <path d="M16 17H8"/>
              <path d="M10 9H8"/>
            </svg>
          </div>
          <span class="node-text">Excel</span>
        </div>
      </div>
    </aside>
  </div>
</template>

<script setup>
import useDragAndDrop from './useDnD'
import { ref, onMounted, onBeforeUnmount } from 'vue'

const { onDragStart } = useDragAndDrop()
const isCollapsed = ref(false)



// Event listener for external toggle
function handleToggleEvent(event) {
  isCollapsed.value = event.detail.collapsed
}

onMounted(() => {
  window.addEventListener('toggle-sidebar', handleToggleEvent)
})

onBeforeUnmount(() => {
  window.removeEventListener('toggle-sidebar', handleToggleEvent)
})
</script>

<style scoped>
.sidebar-container {
  position: relative;
  height: 100%;
  transition: width 0.3s ease-in-out;
  width: 240px;
  overflow: hidden;
}

.sidebar-container.collapsed {
  width: 0;
}

aside {
  width: 100%;
  height: 100%;
  background-color: #2d3748;
  border-right: 1px solid #4a5568;
  padding: 15px;
  font-family: sans-serif;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow-y: auto;
  overflow-x: hidden;
}

.description {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #e2e8f0;
  white-space: nowrap;
}

.description span {
  font-size: 12px;
  font-weight: normal;
  color: #a0aec0;
}

.nodes {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.node-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 5px;
  cursor: grab;
  transition: all 0.2s ease;
  user-select: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  background-color: #1a202c;
  border: 1px solid #4a5568;
  color: #e2e8f0;
  overflow: hidden;
  white-space: nowrap;
}

.node-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border-color: #63b3ed;
}

.node-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  margin-right: 8px;
  border-radius: 4px;
  color: white;
  flex-shrink: 0;
}

.start-node-preview {
  background-color: #2c5282;
  border: 1px solid #4299e1;
}

.start-node-preview .node-icon {
  background-color: #4299e1;
}

.end-node-preview {
  background-color: #702459;
  border: 1px solid #d53f8c;
}

.end-node-preview .node-icon {
  background-color: #d53f8c;
}

.switch-node-preview {
  background-color: #553c9a;
  border: 1px solid #667eea;
}

.switch-node-preview .node-icon {
  background-color: #667eea;
}

.timeout-node-preview {
  background-color: #fed7d7;
  border: 1px solid #e53e3e;
}

.timeout-node-preview .node-icon {
  background-color: #e53e3e;
}

.timeout-node-preview {
  color: #742a2a;
}

.timeout-node-preview:hover {
  border-color: #e53e3e;
  background-color: #fbb6ce;
}

.function-node-preview {
  background-color: #d1fae5;
  border: 1px solid #10b981;
}

.function-node-preview .node-icon {
  background-color: #10b981;
}

.function-node-preview {
  color: #065f46;
}

.function-node-preview:hover {
  border-color: #10b981;
  background-color: #a7f3d0;
}

.ai-agent-node-preview {
  background-color: #eef2ff;
  border: 1px solid #6366f1;
}

.ai-agent-node-preview .node-icon {
  background-color: #6366f1;
}

.ai-agent-node-preview {
  color: #3730a3;
}

.ai-agent-node-preview:hover {
  border-color: #6366f1;
  background-color: #ddd6fe;
}

.excel-node-preview {
  background-color: #f0f9ff;
  border: 1px solid #0ea5e9;
}

.excel-node-preview .node-icon {
  background-color: #0ea5e9;
}

.excel-node-preview {
  color: #0c4a6e;
}

.excel-node-preview:hover {
  border-color: #0ea5e9;
  background-color: #e0f2fe;
}
</style>