export function editNodeAndEdge(nodes = [], edges = []) {
  const editedNodes = nodes.map((node) => {
    const baseNode = {
      id: node.id,
      type: node.type,
      name: node.data?.label || node.type || 'Unknown',
      position: {
        x: node.position?.x || 0,
        y: node.position?.y || 0
      }
    }

    if (node.type === 'startNode') {
      return {
        ...baseNode,
        type: 'inputNode'
      }
    }

    if (node.type === 'endNode') {
      return {
        ...baseNode,
        type: 'outputNode'
      }
    }

    if (node.type === 'timeoutNode') {
      return {
        ...baseNode,
        type: 'customNode',
        content: {
          kind: node.type,
          config: {
            time: String(node.data?.timeout ?? 1)
          }
        }
      }
    }

    if (node.type === 'aiAgentNode') {
      return {
        ...baseNode,
        type: 'customNode',
        content: {
          kind: node.type,
          config: {
            agentId: node.data?.selectedAgent || ''
          }
        }
      }
    }

    if (node.type === 'excelNode') {
      return {
        ...baseNode,
        type: 'customNode',
        content: {
          kind: node.type,
          config: {
            fileName: node.data?.fileName || '',
            excelColumns: node.data?.excelColumns || [],
            selectedColumns: node.data?.selectedColumns || [],
            excelData: node.data?.excelData || null,
            fileType: node.data?.fileType || 'excel',
            operation: node.data?.operation || 'get_data',
            dataLimit: node.data?.dataLimit || 100,
            saveData: node.data?.saveData || false
          }
        }
      }
    }

    if (node.type === 'switchNode') {
      return {
        ...baseNode,
        content: {
          hitPolicy: node.data?.mode,
          statements: (node.data?.conditions || []).map(condition => ({
            id: condition.id,
            condition: condition.expression || ''
          }))
        }
      }
    }

    if (node.type === 'functionNode') {
      return {
        ...baseNode,
        content: {
          source: node.data?.jsCode || ''
        }
      }
    }

    return baseNode
  })

  const editedEdges = edges.map(edge => {
    const edgeData = {
      id: edge.id,
      sourceId: edge.source,
      targetId: edge.target,
      type: 'edge'
    }

    if (edge.sourceHandle && edge.sourceHandle !== 'source') {
      edgeData.sourceHandle = edge.sourceHandle
    }

    return edgeData
  })

  return {
    nodes: editedNodes,
    edges: editedEdges
  }
}
