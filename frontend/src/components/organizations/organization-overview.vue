<template>
  <div
    class="xl:p-5 flex flex-col bg-white border border-gray-200 shadow-sm rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
    <div v-if="loading" class="flex items-center justify-center h-full">
      <MonoIcon class="size-20 animate-spin" name="spin" />
    </div>
    <div v-else-if="error" class="flex items-center h-full p-5">
      <div class="flex flex-col md:flex-row gap-2 items-center justify-center w-full h-full">
        <MonoIcon class="size-20 text-rose-600 dark:text-rose-500 md:pt-2" name="exclamation" />
        <div class="text-center md:text-left">
          <h2 class="text-lg font-semibold text-gray-800 dark:text-neutral-200">
            {{ error.title || $t("general.server_error") }}
          </h2>
          <p class="mt-1 text-sm text-gray-500 dark:text-neutral-400">
            {{ error.message || $t("general.server_error_description") }}
          </p>
        </div>
      </div>
    </div>
    <div v-else class="xl:flex">
      <div
        class="bg-white p-5 xl:p-0 overflow-y-auto relative z-0 block translate-x-0 end-auto bottom-0 rounded-xl xl:rounded-none xl:border-e xl:border-gray-200 dark:bg-neutral-800 dark:xl:border-neutral-700">
        <div class="xl:pe-4 space-y-5 divide-y divide-gray-200 dark:divide-neutral-700">
          <!-- details -->
          <div class="pt-4 first:pt-0 min-w-[300px]">
            <h2 class="text-sm font-semibold text-gray-800 dark:text-neutral-200">
              {{ $t("general.details") }}
            </h2>

            <ul class="mt-3 space-y-2">
              <li v-if="organization.parent" class="flex flex-col">
                <span class="text-xs opacity-50">
                  {{ $t("organizations.overview.parent_organization") }}
                </span>
                <div class="inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-neutral-200 max-w-[230px]">
                  <MonoIcon name="organization" class="flex-shrink-0 size-4 text-gray-600 dark:text-neutral-400" />
                  <span class="whitespace-break-spaces">
                    <router-link :to="{
                      name: 'organizations-view',
                      params: { id: organization.parent_id },
                    }" class="inline hover:text-teal-600 dark:hover:text-teal-500">
                      {{ organization.parent.name }}
                    </router-link>
                  </span>
                </div>
              </li>
              <li class="flex flex-col">
                <span class="text-xs opacity-50">
                  {{ $t("organizations.overview.timezone") }}
                </span>
                <div class="inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-neutral-200">
                  <MonoIcon name="map-pin-house" class="flex-shrink-0 size-4 text-gray-600 dark:text-neutral-400" />

                  {{ organization.timezone }}
                </div>
              </li>
              <li class="flex flex-col">
                <span class="text-xs opacity-50">
                  {{ $t("organizations.overview.created_at") }}
                </span>
                <div class="inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-neutral-200">
                  <MonoIcon name="calendar" class="flex-shrink-0 size-4 text-gray-600 dark:text-neutral-400" />

                  {{ $dayjs(organization.created_at).format("MMM D, YYYY HH:mm") }}
                </div>
              </li>
              <li v-if="organization.domain_address" class="flex flex-col">
                <span class="text-xs opacity-50">
                  {{ $t("organizations.overview.domain_address") }}
                </span>
                <div class="inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-neutral-200">
                  <MonoIcon name="world" class="flex-shrink-0 size-4 text-gray-600 dark:text-neutral-400" />
                  <a :href="organization.domain_address" target="_blank" rel="noopener noreferrer"
                    class="hover:text-teal-600 dark:hover:text-teal-500">
                    {{ organization.domain_address }}
                  </a>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <div class="px-5 py-2 xl:py-0 grow space-y-5">
        <div class="grid grid-cols-2 lg:grid-cols-3 gap-2 md:gap-3 xl:gap-5">

          <!-- Total Simulates -->
          <div
            class="relative overflow-hidden p-4 sm:p-5 bg-white border border-gray-200 rounded-xl shadow-sm before:absolute before:top-0 before:end-0 before:size-full before:bg-gradient-to-br before:from-blue-100 before:via-transparent before:blur-xl dark:bg-neutral-800 dark:border-neutral-700 dark:before:from-blue-800/30 dark:before:via-transparent">
            <div class="relative">
              <div class="flex gap-3 items-center">
                <span
                  class="inline-flex justify-center items-center size-8 md:size-10 rounded-lg bg-white text-gray-700 shadow dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-400">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
                    stroke="currentColor" class="flex-shrink-0 size-4 md:size-5 text-blue-500">
                    <path stroke-linecap="round" stroke-linejoin="round"
                      d="M9.75 3.104v5.714a2.25 2.25 0 0 1-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 0 1 4.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 14.5M14.25 3.104c.251.023.501.05.75.082M19.8 14.5l-2.436 2.436a2.25 2.25 0 0 1-1.591.659h-3.546a2.25 2.25 0 0 1-1.591-.659L8.2 14.5m11.6 0V16.5a2.25 2.25 0 0 1-2.25 2.25h-6a2.25 2.25 0 0 1-2.25-2.25V14.5" />
                  </svg>
                </span>

                <h2 class="text-sm md:text-base text-gray-800 dark:text-neutral-200">
                  {{ $t("organizations.overview.total_simulates") }}
                </h2>
              </div>

              <div class="mt-4 flex items-center justify-center">
                <h3 class="text-lg md:text-3xl font-semibold text-gray-800 dark:text-neutral-200">
                  {{ dashboardData.total_simulate_count ?? 0 }}
                </h3>
              </div>
            </div>
          </div>

          <!-- Total Evaluations -->
          <div
            class="relative overflow-hidden p-4 sm:p-5 bg-white border border-gray-200 rounded-xl shadow-sm before:absolute before:top-0 before:end-0 before:size-full before:bg-gradient-to-br before:from-purple-100 before:via-transparent before:blur-xl dark:bg-neutral-800 dark:border-neutral-700 dark:before:from-purple-800/30 dark:before:via-transparent">
            <div class="relative">
              <div class="flex gap-3 items-center">
                <span
                  class="inline-flex justify-center items-center size-8 md:size-10 rounded-lg bg-white text-gray-700 shadow dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-400">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
                    stroke="currentColor" class="flex-shrink-0 size-4 md:size-5 text-purple-500">
                    <path stroke-linecap="round" stroke-linejoin="round"
                      d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                  </svg>
                </span>

                <h2 class="text-sm md:text-base text-gray-800 dark:text-neutral-200">
                  {{ $t("organizations.overview.total_evaluations") }}
                </h2>
              </div>

              <div class="mt-4 flex items-center justify-center">
                <h3 class="text-lg md:text-3xl font-semibold text-gray-800 dark:text-neutral-200">
                  {{ dashboardData.total_evaluation_count ?? 0 }}
                </h3>
              </div>
            </div>
          </div>

          <!-- Total Success -->
          <div
            class="relative overflow-hidden p-4 sm:p-5 bg-white border border-gray-200 rounded-xl shadow-sm before:absolute before:top-0 before:end-0 before:size-full before:bg-gradient-to-br before:from-green-100 before:via-transparent before:blur-xl dark:bg-neutral-800 dark:border-neutral-700 dark:before:from-green-800/30 dark:before:via-transparent">
            <div class="relative">
              <div class="flex gap-3 items-center">
                <span
                  class="inline-flex justify-center items-center size-8 md:size-10 rounded-lg bg-white text-gray-700 shadow dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-400">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
                    stroke="currentColor" class="flex-shrink-0 size-4 md:size-5 text-green-500">
                    <path stroke-linecap="round" stroke-linejoin="round"
                      d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                  </svg>
                </span>

                <h2 class="text-sm md:text-base text-gray-800 dark:text-neutral-200">
                  {{ $t("organizations.overview.total_success") }}
                </h2>
              </div>

              <div class="mt-4 flex items-center justify-center">
                <h3 class="text-lg md:text-3xl font-semibold text-gray-800 dark:text-neutral-200">
                  {{ dashboardData.total_success_count ?? 0 }}
                </h3>
              </div>
            </div>
          </div>

          <!-- Total Errors -->
          <div
            class="relative overflow-hidden p-4 sm:p-5 bg-white border border-gray-200 rounded-xl shadow-sm before:absolute before:top-0 before:end-0 before:size-full before:bg-gradient-to-br before:from-red-100 before:via-transparent before:blur-xl dark:bg-neutral-800 dark:border-neutral-700 dark:before:from-red-800/30 dark:before:via-transparent">
            <div class="relative">
              <div class="flex gap-3 items-center">
                <span
                  class="inline-flex justify-center items-center size-8 md:size-10 rounded-lg bg-white text-gray-700 shadow dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-400">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
                    stroke="currentColor" class="flex-shrink-0 size-4 md:size-5 text-red-500">
                    <path stroke-linecap="round" stroke-linejoin="round"
                      d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
                  </svg>
                </span>

                <h2 class="text-sm md:text-base text-gray-800 dark:text-neutral-200">
                  {{ $t("organizations.overview.total_errors") }}
                </h2>
              </div>

              <div class="mt-4 flex items-center justify-center">
                <h3 class="text-lg md:text-3xl font-semibold text-gray-800 dark:text-neutral-200">
                  {{ dashboardData.total_error_count ?? 0 }}
                </h3>
              </div>
            </div>
          </div>

          <!-- Total Versions -->
          <div
            class="relative overflow-hidden p-4 sm:p-5 bg-white border border-gray-200 rounded-xl shadow-sm before:absolute before:top-0 before:end-0 before:size-full before:bg-gradient-to-br before:from-indigo-100 before:via-transparent before:blur-xl dark:bg-neutral-800 dark:border-neutral-700 dark:before:from-indigo-800/30 dark:before:via-transparent">
            <div class="relative">
              <div class="flex gap-3 items-center">
                <span
                  class="inline-flex justify-center items-center size-8 md:size-10 rounded-lg bg-white text-gray-700 shadow dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-400">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
                    stroke="currentColor" class="flex-shrink-0 size-4 md:size-5 text-indigo-500">
                    <path stroke-linecap="round" stroke-linejoin="round"
                      d="M5.25 14.25h13.5m-13.5 0a3 3 0 0 1-3-3V6a3 3 0 0 1 3-3h13.5a3 3 0 0 1 3 3v5.25a3 3 0 0 1-3 3m-16.5 0a3 3 0 0 1 3 3v6a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3v-6a3 3 0 0 1 3-3m13.5 0a3 3 0 0 1 3 3v6a3 3 0 0 1-3 3H8.25a3 3 0 0 1-3-3v-6a3 3 0 0 1 3-3" />
                  </svg>
                </span>

                <h2 class="text-sm md:text-base text-gray-800 dark:text-neutral-200">
                  {{ $t("organizations.overview.total_versions") }}
                </h2>
              </div>

              <div class="mt-4 flex items-center justify-center">
                <h3 class="text-lg md:text-3xl font-semibold text-gray-800 dark:text-neutral-200">
                  {{ dashboardData.total_version_count ?? 0 }}
                </h3>
              </div>
            </div>
          </div>

          <!-- Total Users -->
          <div
            class="relative overflow-hidden p-4 sm:p-5 bg-white border border-gray-200 rounded-xl shadow-sm before:absolute before:top-0 before:end-0 before:size-full before:bg-gradient-to-br before:from-yellow-100 before:via-transparent before:blur-xl dark:bg-neutral-800 dark:border-neutral-700 dark:before:from-yellow-800/30 dark:before:via-transparent">
            <div class="relative">
              <div class="flex gap-3 items-center">
                <span
                  class="inline-flex justify-center items-center size-8 md:size-10 rounded-lg bg-white text-gray-700 shadow dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-400">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
                    stroke="currentColor" class="flex-shrink-0 size-4 md:size-5 text-yellow-500">
                    <path stroke-linecap="round" stroke-linejoin="round"
                      d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
                  </svg>
                </span>

                <h2 class="text-sm md:text-base text-gray-800 dark:text-neutral-200">
                  {{ $t("organizations.overview.total_admins") }}
                </h2>
              </div>

              <div class="mt-4 flex items-center justify-center">
                <h3 class="text-lg md:text-3xl font-semibold text-gray-800 dark:text-neutral-200">
                  {{ dashboardData.total_admin_count ?? 0 }}
                </h3>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useOrganizationStore } from "@/store/submodules/organization";
import { useDashStore } from "@/store/submodules/dashboard";

const store = useOrganizationStore();
const dashStore = useDashStore();
const route = useRoute();
const toast = inject("toast");

const props = defineProps({
  detail: {
    type: Object,
    default: () => ({}),
  },
});

const loading = ref(true);
const error = ref(null);
const organization = ref({});
const organizationDetail = ref({});
const dashboardData = ref({});


onMounted(async () => {
  try {
    const res = await store.Read(route.params.id);
    organization.value = res;

    if (res.parent_id !== "00000000-0000-0000-0000-000000000000") {
      const parentRes = await store.Read(res.parent_id);
      organization.value.parent = parentRes;
    }
  } catch (err) {
    console.error(err);
    error.value = {
      code: "500",
      title: "Internal Error",
      message: "An unexpected error occurred. Please try again later.",
    };
  }

  try {
    const res = await store.Detail(route.params.id);
    organizationDetail.value = res;
    console.log(res);
  } catch (err) {
    console.error(err);
    error.value = {
      code: "500",
      title: "Internal Error",
      message: "An unexpected error occurred. Please try again later.",
    };
  }

  try {
    const dashRes = await dashStore.Get();
    dashboardData.value = dashRes;
    console.log('Dashboard data:', dashRes);
  } catch (err) {
    console.error('Dashboard API Error:', err);
  }

  loading.value = false;
});
</script>
