<template>
  <div class="p-4 bg-white dark:bg-neutral-800 rounded-lg shadow">
    <div v-if="loading" class="flex items-center justify-center h-64">
      <MonoIcon class="size-20 animate-spin" name="spin" />
    </div>

    <div v-else>
      <div class="border border-gray-200 dark:border-neutral-700 rounded-lg overflow-hidden mb-4">
        <div class="p-0">
          <div ref="monacoContainer" class="w-full border-0"></div>
        </div>
      </div>
      <div class="flex justify-center">
        <button @click="saveDefaultRequest" :disabled="saving"
          class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 flex items-center gap-2">
          <MonoIcon 
            class="size-4" 
            name="pencil" />
            {{ saving ? $t('general.saving') : $t('general.save') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, inject } from 'vue';
import { useRoute } from 'vue-router';
import { useSimulateStore } from '@/store/submodules/simulate';
import * as monaco from 'monaco-editor';

const { t } = useI18n();
const route = useRoute();
const store = useSimulateStore();
const toast = inject('toast');

const loading = ref(true);
const saving = ref(false);
const jsonData = ref({});
const defaultRequestId = ref(null);

// Monaco editor refs
const monacoContainer = ref(null);
let monacoEditor = null;
const editorReady = ref(false);

function initMonacoEditor() {
  if (!monacoContainer.value || monacoEditor) {
    return;
  }

  const requestData = JSON.stringify(jsonData.value, null, 2);

  monacoEditor = monaco.editor.create(monacoContainer.value, {
    value: requestData,
    language: 'json',
    theme: 'vs-dark',
    lineNumbers: 'on',
    automaticLayout: false,
    minimap: { enabled: false },
    scrollBeyondLastLine: false,
    wordWrap: 'on',
    formatOnPaste: false,
    formatOnType: false,
    fontSize: 15,
    readOnly: false,
    scrollbar: {
      vertical: 'auto',
      horizontal: 'auto'
    },
    contextmenu: true,
    selectOnLineNumbers: true,
    overviewRulerLanes: 0,
    hideCursorInOverviewRuler: true,
    overviewRulerBorder: false
  });

  monacoEditor.onDidChangeModelContent(() => {
    try {
      const value = monacoEditor.getValue();
      const parsed = JSON.parse(value);
      jsonData.value = parsed;
    } catch (e) {
    }
  });

  setTimeout(() => {
    updateEditorHeight();
  }, 50);

  editorReady.value = true;
}

function updateEditorHeight() {
  if (monacoEditor && monacoContainer.value) {
    const model = monacoEditor.getModel();
    if (model) {
      const lineCount = model.getLineCount();
      const lineHeight = monacoEditor.getOption(monaco.editor.EditorOption.lineHeight);
      const contentHeight = lineCount * lineHeight + 40;
      const height = Math.max(200, Math.min(600, contentHeight));

      monacoContainer.value.style.height = `${height}px`;

      monacoEditor.layout({
        width: monacoContainer.value.offsetWidth,
        height: height
      });
    }
  }
}

async function loadDefaultRequest() {
  loading.value = true;
  try {
    const response = await store.GetDefaultRequest(route.params.id, '2');
    if (response) {
      defaultRequestId.value = response.id;
      jsonData.value = typeof response.value === 'string'
        ? JSON.parse(response.value)
        : response.value;
    } else {
      jsonData.value = {};
    }
  } catch (err) {
    jsonData.value = {};
  } finally {
    loading.value = false;

    await nextTick();
    setTimeout(() => {
      initMonacoEditor();
    }, 200);
  }
}

async function saveDefaultRequest() {
  saving.value = true;
  try {
    let contentToSave = jsonData.value;
    if (monacoEditor) {
      try {
        const editorValue = monacoEditor.getValue();
        contentToSave = JSON.parse(editorValue);
      } catch (e) {
        saving.value = false;
        return;
      }
    }

    const payload = {
      simulate_id: route.params.id,
      type: 2,
      value: JSON.stringify(contentToSave, null, 2)
    };
    if (defaultRequestId.value) {
      payload.id = defaultRequestId.value;
    }
    const res = await store.AddDefaultRequest(payload);
    defaultRequestId.value = res.id;
    jsonData.value = contentToSave;
    toast.success(t('simulates.default_request.success.saved'));
  } catch {
    toast.error(t('simulates.default_request.error.save'));
  } finally {
    saving.value = false;
  }
}

onMounted(loadDefaultRequest);

onUnmounted(() => {
  if (monacoEditor) {
    monacoEditor.dispose();
    monacoEditor = null;
  }
  editorReady.value = false;
});
</script>
