# important: alpine image is not compatible because of the cgo
FROM golang:1.24 AS builder

WORKDIR /src/pars-vue

COPY . .

RUN go mod download

RUN GOOS=linux go build -ldflags="-s -w -linkmode 'external' -extldflags '-static'" -o pars-vue main.go

FROM scratch

# take env from build args
ARG VERSION
ENV APP_VERSION=$VERSION

COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt

WORKDIR /bin/pars-vue

COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo
COPY --from=builder /src/pars-vue/config.yaml /bin/pars-vue/config.yaml
COPY --from=builder /src/pars-vue/pars-vue .

CMD [ "./pars-vue" ]