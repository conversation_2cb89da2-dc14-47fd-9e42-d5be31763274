package simulate

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	zen "github.com/gorules/zen-go"
	"github.com/parsguru/pars-vue/pkg/config"
	"github.com/parsguru/pars-vue/pkg/constants/query"
	"github.com/parsguru/pars-vue/pkg/domains/nodes"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/helpers"
	"github.com/parsguru/pars-vue/pkg/state"
	"github.com/parsguru/pars-vue/pkg/utils"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type Repository interface {
	Simulate(payload *dtos.Simulate, ctx *gin.Context, t trace.Tracer) (dtos.ResponseSimulate, error)
	UpdateOrCreateSimulate(payload *dtos.Simulate, ctx *gin.Context, t trace.Tracer) (string, error)
	SimulateUpdateName(ctx *gin.Context, req dtos.RequestForUpdateSimulateName, t trace.Tracer) error
	GetSimulations(page, perpage int, version_id string, ctx *gin.Context, t trace.Tracer) (dtos.PaginatedData, error)
	GetSimulationRules(page, perpage int, ctx *gin.Context, t trace.Tracer) (dtos.PaginatedData, error)
	SimulateRule(simulate_id string, ctx *gin.Context, t trace.Tracer) (dtos.SimulateResponse, error)
	SimulateDetail(ctx *gin.Context, simulate_id string, t trace.Tracer) (dtos.SimulateResponseForDetail, error)
	DeleteSimulate(simulate_id string, ctx context.Context, t trace.Tracer) error
	SimByID(ctx context.Context, req dtos.RequestForSimByID, t trace.Tracer) (dtos.ResponseSimulate, error)

	AddDefaultRequest(ctx *gin.Context, t trace.Tracer, req dtos.RequestForDefaultRequest) error
	getDefaultRequest(ctx *gin.Context, t trace.Tracer, simulate_id, def_req_type string) (dtos.ResponseForDefaultRequest, error)
	DeleteDefaultRequest(ctx *gin.Context, t trace.Tracer, id string) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func readTestFile(key string) ([]byte, error) {
	return []byte(key), nil
}

// DeleteSimulate implements Repository.
func (r *repository) DeleteSimulate(simulate_id string, ctx context.Context, t trace.Tracer) error {
	r.db.Transaction(func(tx *gorm.DB) error {
		if err := tx.WithContext(ctx).
			Where("id=? AND organization_id=?", simulate_id, state.CurrentAdminOrganization(ctx)).
			Delete(&entities.Simulate{}).Error; err != nil {
			return err
		}

		query := tx.WithContext(ctx).
			Where("simulate_id=? AND organization_id=?", simulate_id, state.CurrentAdminOrganization(ctx))

		if err := query.Unscoped().Delete(&entities.Node{}).Error; err != nil {
			return err
		}

		if err := query.Unscoped().Delete(&entities.Edge{}).Error; err != nil {
			return err
		}

		if err := query.Unscoped().Delete(&entities.SimulateLog{}).Error; err != nil {
			return err
		}

		if err := query.Unscoped().Delete(&entities.DefaultRequest{}).Error; err != nil {
			return err
		}

		if err := query.Unscoped().Delete(&entities.SimulateOutput{}).Error; err != nil {
			return err
		}

		if err := query.Unscoped().Delete(&entities.Evaluations{}).Error; err != nil {
			return err
		}

		return nil
	})
	return nil
}

func (r *repository) SimByID(ctx context.Context, req dtos.RequestForSimByID, t trace.Tracer) (dtos.ResponseSimulate, error) {
	helpers.PushToTrace(t, ctx, "simulate.service.SimByID", "simulate.service.SimByID")

	var (
		output       dtos.ResponseSimulate
		evaluate     entities.Evaluations
		simulate_log entities.SimulateLog
	)

	evaluate.IsThereError = false

	// -----> parse simulate token start
	jwt := utils.JwtWrapper{
		SecretKey: config.ReadValue().JwtSecret,
		Issuer:    config.ReadValue().JwtIssuer,
	}

	if !jwt.ValidateSimulateToken(req.Token) {
		return output, errors.New("invalid token")
	}

	claims, err := jwt.ParseSimulateToken(req.Token)
	if err != nil {
		return output, err
	}
	// parse simulate token end <-----

	defer func() {
		evaluate.ProtoType = "http"
		evaluate.OrganizationID = uuid.MustParse(claims.OrganizationID)
		evaluate.AdminID = uuid.MustParse(claims.AdminID)
		if claims.SimulateID != "" {
			evaluate.SimulateID = uuid.MustParse(claims.SimulateID)
		}
		r.db.WithContext(ctx).Create(&evaluate)

		if claims.SimulateID != "" {
			simulate_log.SimulateID = uuid.MustParse(claims.SimulateID)
			simulate_log.OrganizationID = uuid.MustParse(claims.OrganizationID)
			simulate_log.AdminID = uuid.MustParse(claims.AdminID)
			simulate_log.Proto = "http"
			simulate_log.Source = "external"
			simulate_log.Domain = config.ReadValue().Host
		} else {
			return
		}
		r.db.WithContext(ctx).Create(&simulate_log)
	}()

	var (
		current_simulate entities.Simulate
		m_node           []dtos.Node
		m_edge           []dtos.Edge
	)

	if err := r.db.Where(query.WhereID, claims.SimulateID).
		Where(query.WhereOrganizationID, claims.OrganizationID).
		Preload(clause.Associations).
		First(&current_simulate).Error; err != nil {
		return output, err
	}

	for _, v := range current_simulate.Nodes {
		var s_node dtos.Node
		if v.Content != nil {
			s_node.Content = v.Content
		} else {
			s_node.Content = v.FunctionContent
		}
		s_node.ID = v.NodeID
		s_node.Name = v.Name
		s_node.Position = v.Position
		s_node.Type = v.Type

		m_node = append(m_node, s_node)
	}

	for _, v := range current_simulate.Edges {
		var s_edge dtos.Edge
		s_edge.ID = v.ID
		s_edge.SourceHandle = v.SourceHandle
		s_edge.SourceID = v.SourceID
		s_edge.TargetID = v.TargetID
		s_edge.Type = v.Type

		m_edge = append(m_edge, s_edge)
	}

	content_map := make(map[string]any)

	content_map["nodes"] = m_node
	content_map["edges"] = m_edge

	graph, err := json.Marshal(content_map)
	if err != nil {
		evaluate.IsThereError = true
		simulate_log.Message = err.Error()
		simulate_log.Type = "error"

		return output, err
	}

	m_context, err := json.Marshal(req.Context)
	if err != nil {
		evaluate.IsThereError = true
		simulate_log.Message = err.Error()
		simulate_log.Type = "error"
		return output, err
	}
	simulate_log.Request = string(m_context)

	req.Context["_user_id"] = claims.AdminID
	req.Context["_simulate_id"] = claims.SimulateID
	req.Context["_organization_id"] = claims.OrganizationID

	evaluate.Content = string(graph)
	evaluate.Context = string(m_context)

	engine := zen.NewEngine(zen.EngineConfig{
		Loader:            readTestFile,
		CustomNodeHandler: nodes.CustomNodeHandler,
	})

	defer engine.Dispose()
	zenOutput, err := engine.EvaluateWithOpts(
		string(graph),
		req.Context,
		zen.EvaluationOptions{Trace: true},
	)
	if err != nil {
		simulate_log.Type = "error"
		simulate_log.Message = err.Error()
		evaluate.IsThereError = true
		return output, err
	}

	output.Performance = zenOutput.Performance
	output.Trace = zenOutput.Trace
	output.Result = zenOutput.Result

	var (
		resulJSONtmap map[string]any
	)

	zenOutput.Result = removeUnderlineValues(zenOutput.Result)

	simulate_log.Type = "success"
	simulate_log.Message = "Simulate is success"
	simulate_log.Response = string(output.Result)

	evaluate.SimulateResult = resulJSONtmap
	output.Result = removeUnderlineValues(output.Result)

	return output, nil
}

// -----> This function update or create simulate and it doesn't control simulate is vorking or not
func (r *repository) UpdateOrCreateSimulate(payload *dtos.Simulate, ctx *gin.Context, t trace.Tracer) (string, error) {
	helpers.PushToTrace(t, ctx, "simulate.service.UpdateOrCreateSimulate", "simulate.service.UpdateOrCreateSimulate")
	var (
		simulate_id string
		err         error
	)

	var (
		nodeEntities   []*entities.Node
		edgeEntities   []*entities.Edge
		nodes          []dtos.Node
		edges          []dtos.Edge
		input_control  int = 0
		output_control int = 0
	)

	// -----> node and edge control started
	if err := utils.MapToStruct(payload.Content["nodes"], &nodes); err != nil {
		return simulate_id, err
	}

	if err = utils.MapToStruct(payload.Content["edges"], &edges); err != nil {
		return simulate_id, err
	}

	for _, v := range nodes {
		if v.Type == "inputNode" {
			input_control++
		}
		if v.Type == "outputNode" {
			output_control++
		}
	}

	if input_control > 1 {
		// TODO: localization
		return simulate_id, fmt.Errorf("there must be only one input node")
	}

	if (input_control + output_control) < 2 {
		// TODO: localization
		return simulate_id, fmt.Errorf("there must be at least one input and one output node")
	}

	if len(edges) == 0 {
		// TODO: localization
		return simulate_id, fmt.Errorf("there must be at least one edge")
	}

	tx := r.db.Begin()

	var (
		new_version_name   string
		new_version_number int
		version_id         uuid.UUID
		version_err        error
		simulate           entities.Simulate
		previous_simulate  entities.Simulate
	)

	// 1. new simulate, 2. update as new version, 3. update current version

	if payload.Status == 1 {
		version_id = uuid.New()
		new_version_name, new_version_number, version_err = GetNewVersion("")
		if version_err != nil {
			tx.Rollback()
			return simulate_id, version_err
		}

		simulate, err = setSimulate(payload, ctx)
		if err != nil {
			tx.Rollback()
			return simulate_id, err
		}

		simulate.VersionName = new_version_name
		simulate.VersionNumber = new_version_number
		simulate.VersionID = version_id

		if err := tx.WithContext(ctx).
			Model(&entities.Simulate{}).
			Create(&simulate).Error; err != nil {
			tx.Rollback()
			return simulate_id, err
		}

		jwt := utils.JwtWrapper{
			SecretKey: config.ReadValue().JwtSecret,
			Issuer:    config.ReadValue().JwtIssuer,
		}

		token, _ := jwt.GenerateSimulateJWT(ctx, simulate.ID.String(), simulate.OrganizationID.String(), simulate.AdminID.String(), version_id.String(), new_version_name, 999)

		simulate.AccessToken = token

		if err := tx.WithContext(ctx).
			Model(&entities.Simulate{}).
			Where("id=?", simulate.ID).
			Updates(&simulate).Error; err != nil {
			tx.Rollback()
			return simulate_id, err
		}

		simulate_id = simulate.ID.String()
	} else {
		if err := tx.WithContext(ctx).
			Where("id=?", payload.ID).
			Where("organization_id=?", state.CurrentAdminOrganization(ctx)).
			Preload(clause.Associations).
			First(&previous_simulate).Error; err != nil {
			tx.Rollback()
			return simulate_id, err
		}

		var simulates_for_control []entities.Simulate
		if err := tx.WithContext(ctx).
			Where("version_id=?", previous_simulate.VersionID).
			Where("organization_id=?", state.CurrentAdminOrganization(ctx)).
			Find(&simulates_for_control).Error; err != nil {
			tx.Rollback()
			return simulate_id, err
		}
		for _, v := range simulates_for_control {
			if v.Name == payload.Name {
				tx.Rollback()
				return simulate_id, fmt.Errorf("cannot create new version with same name")
			}
		}

		if payload.Status == 2 {
			version_id = previous_simulate.VersionID
			new_version_name, new_version_number, version_err = GetNewVersion(previous_simulate.VersionName)
			if version_err != nil {
				tx.Rollback()
				return simulate_id, version_err
			}

		}
		if payload.Status == 3 {
			version_id = previous_simulate.VersionID
			new_version_name = previous_simulate.VersionName
			new_version_number = previous_simulate.VersionNumber
		}

		simulate, err = setSimulate(payload, ctx)
		if err != nil {
			tx.Rollback()
			return simulate_id, err
		}

		simulate.VersionName = new_version_name
		simulate.VersionNumber = new_version_number
		simulate.VersionID = version_id
		simulate.AccessToken = previous_simulate.AccessToken

		if payload.Status == 2 {
			if err := tx.WithContext(ctx).
				Model(&entities.Simulate{}).
				Create(&simulate).Error; err != nil {
				tx.Rollback()
				return simulate_id, err
			}
		}
		if payload.Status == 3 {
			if err := tx.WithContext(ctx).
				Model(&entities.Simulate{}).
				Where("id=?", previous_simulate.ID).
				Where("organization_id=?", previous_simulate.OrganizationID).
				Updates(&simulate).Error; err != nil {
				return simulate_id, err
			}

			if err := tx.Where("simulate_id", simulate.ID).
				Where("version_id", simulate.VersionID).
				Where("version_name", simulate.VersionName).
				Where("organization_id", state.CurrentAdminOrganization(ctx)).
				Unscoped().Delete(&nodeEntities).Error; err != nil {
				return simulate_id, err
			}

			if err := tx.Where("simulate_id", simulate.ID).
				Where("version_id", simulate.VersionID).
				Where("version_name", simulate.VersionName).
				Where("organization_id", state.CurrentAdminOrganization(ctx)).
				Unscoped().Delete(&edgeEntities).Error; err != nil {
				return simulate_id, err
			}
		}

		var dr entities.DefaultRequest
		r.db.WithContext(ctx).
			Model(&entities.DefaultRequest{}).
			Where("simulate_id=?", previous_simulate.ID).
			Where("organization_id=?", state.CurrentAdminOrganization(ctx)).
			First(&dr)

		if dr.ID != uuid.Nil {
			def_request_for_new_version := entities.DefaultRequest{
				Value:          dr.Value,
				SimulateID:     uuid.MustParse(simulate_id),
				OrganizationID: state.CurrentAdminOrganization(ctx),
				AdminID:        state.CurrentAdminUser(ctx),
				Type:           2,
			}

			r.db.WithContext(ctx).
				Model(&entities.DefaultRequest{}).
				Create(&def_request_for_new_version)
		}

		simulate_id = simulate.ID.String()
	}

	for _, n := range nodes {
		node, err := GetNode(n, simulate.ID, version_id, simulate.OrganizationID, simulate.AdminID, new_version_name)
		if err != nil {
			tx.Rollback()
			return simulate_id, err
		}
		nodeEntities = append(nodeEntities, &node)
	}

	if len(nodeEntities) > 0 {
		if err := tx.Create(&nodeEntities).Error; err != nil {
			tx.Rollback()
			return simulate_id, err
		}
	}

	for _, e := range edges {
		edge := GetEdge(e, simulate.ID, simulate.VersionID, simulate.OrganizationID, simulate.AdminID, simulate.VersionName, payload.Status)
		edgeEntities = append(edgeEntities, &edge)
	}

	if len(edgeEntities) > 0 {
		if err := tx.Create(&edgeEntities).Error; err != nil {
			tx.Rollback()
			return simulate_id, err
		}
	}

	tx.Commit()
	return simulate_id, err
}

func (r *repository) SimulateUpdateName(ctx *gin.Context, req dtos.RequestForUpdateSimulateName, t trace.Tracer) error {
	var simulate entities.Simulate
	if err := r.db.WithContext(ctx).
		Model(&entities.Simulate{}).
		Where("id = ? AND organization_id = ?", req.SimulateID, state.CurrentAdminOrganization(ctx)).
		First(&simulate).Error; err != nil {
		return err
	}

	if simulate.ID == uuid.Nil {
		return fmt.Errorf("simulate not found")
	}

	simulate.Name = req.Name

	if err := r.db.Save(&simulate).Error; err != nil {
		return err
	}

	return nil
}

// Simulate implements Repository.
func (r *repository) Simulate(payload *dtos.Simulate, ctx *gin.Context, t trace.Tracer) (dtos.ResponseSimulate, error) {

	helpers.PushToTrace(t, ctx, "simulate.service.Simulate", "simulate.service.Simulate")

	var (
		output       dtos.ResponseSimulate
		evaluate     entities.Evaluations
		simulate_log entities.SimulateLog
	)

	evaluate.IsThereError = false

	defer func() {
		evaluate.ProtoType = "http"
		evaluate.OrganizationID = state.CurrentAdminOrganization(ctx)
		evaluate.AdminID = state.CurrentAdminUser(ctx)
		if payload.ID != "" {
			evaluate.SimulateID = uuid.MustParse(payload.ID)
		}
		r.db.WithContext(ctx).Create(&evaluate)

		if payload.ID != "" {
			simulate_log.SimulateID = uuid.MustParse(payload.ID)
			simulate_log.OrganizationID = state.CurrentAdminOrganization(ctx)
			simulate_log.AdminID = state.CurrentAdminUser(ctx)
			simulate_log.Proto = "http"
			simulate_log.Source = "editor"
		} else {
			return
		}
		r.db.WithContext(ctx).Create(&simulate_log)
	}()

	graph, err := json.Marshal(payload.Content)
	if err != nil {
		evaluate.IsThereError = true
		return output, err
	}

	m_context, err := json.Marshal(payload.Context)
	if err != nil {
		evaluate.IsThereError = true
		return output, err
	}
	simulate_log.Request = string(m_context)

	/* Convert Part Start */
	var contextMap map[string]interface{}
	if err := json.Unmarshal(m_context, &contextMap); err != nil {
		return output, err
	}

	contextMap["_user_id"] = state.CurrentAdminUser(ctx)
	contextMap["_organization_id"] = state.CurrentAdminOrganization(ctx)
	contextMap["_simulate_id"] = payload.ID

	m_context, err = json.Marshal(contextMap)
	if err != nil {
		evaluate.IsThereError = true
		return output, err
	}
	/* Convert Part End */

	evaluate.Content = string(graph)
	evaluate.Context = string(m_context)
	payload.Context = contextMap

	engine := zen.NewEngine(zen.EngineConfig{
		Loader:            readTestFile,
		CustomNodeHandler: nodes.CustomNodeHandler,
	})

	defer engine.Dispose()
	zenOutput, err := engine.EvaluateWithOpts(
		string(graph),
		payload.Context,
		zen.EvaluationOptions{Trace: true},
	)
	if err != nil {
		simulate_log.Type = "error"
		simulate_log.Message = err.Error()
		evaluate.IsThereError = true
		return output, err
	}

	output = dtos.ResponseSimulate{
		Performance: zenOutput.Performance,
		Trace:       zenOutput.Trace,
		Result:      zenOutput.Result,
	}

	output.Result = removeUnderlineValues(output.Result)

	var (
		resulJSONtbyte []byte
		resulJSONtmap  map[string]any
	)

	resulJSONtbyte, err = output.Result.MarshalJSON()
	if err != nil {
		simulate_log.Type = "error"
		simulate_log.Message = err.Error()
		simulate_log.Response = ""
		evaluate.IsThereError = true
		return output, err
	}

	err = json.Unmarshal(resulJSONtbyte, &resulJSONtmap)
	if err != nil {
		evaluate.IsThereError = true
		return output, err
	}
	simulate_log.Type = "success"
	simulate_log.Message = "Simulate is success"
	simulate_log.Response = string(resulJSONtbyte)

	evaluate.SimulateResult = resulJSONtmap
	return output, err
}

func removeUnderlineValues(raw json.RawMessage) json.RawMessage {
	first := make(map[string]interface{})
	json.Unmarshal(raw, &first)
	second := make(map[string]interface{})
	for key, value := range first {
		if key == "_organization_id" || key == "_simulate_id" || key == "_user_id" || key == "_code" || key == "_sms_node_message" || key == "_mail_node_message" {
			continue
		} else {
			second[key] = value
		}
	}
	updatedJSON, _ := json.Marshal(second)
	return json.RawMessage(updatedJSON)
}

// GetSimulations implements Repository.
func (r *repository) GetSimulations(page, perpage int, version_id string, ctx *gin.Context, t trace.Tracer) (dtos.PaginatedData, error) {
	preload := []string{"SimulateOutput", "Nodes", "Edges"}
	return getSimulates(page, perpage, r.db, preload, version_id, ctx)
}

// GetSimulationRules implements Repository.
func (r *repository) GetSimulationRules(page, perpage int, ctx *gin.Context, t trace.Tracer) (dtos.PaginatedData, error) {
	preload := []string{"Nodes", "Edges"}
	return getSimulates(page, perpage, r.db, preload, "", ctx)
}

// SimulateRule implements Repository.
func (r *repository) SimulateRule(simulate_id string, ctx *gin.Context, t trace.Tracer) (dtos.SimulateResponse, error) {
	helpers.PushToTrace(t, ctx, "simulate.service.SimulateRule", "simulate.service.SimulateRule")

	var (
		simulate    entities.Simulate
		simulateDTO dtos.SimulateResponse
	)

	err := r.db.Where(query.WhereID, simulate_id).
		Where(query.WhereOrganizationID, state.CurrentAdminOrganization(ctx)).
		Preload(clause.Associations).
		First(&simulate).Error

	simulateDTO.Context = simulate.Context
	simulateDTO.ID = simulate.ID
	simulateDTO.Name = simulate.Name
	simulateDTO.OrganizationID = simulate.OrganizationID.String()
	simulateDTO.AdminID = simulate.AdminID.String()
	simulateDTO.SimulateOutput = simulate.SimulateOutput
	simulateDTO.VersionName = simulate.VersionName
	simulateDTO.CreatedAt = simulate.CreatedAt.Format("2006-01-02 15:04:05")
	simulateDTO.VersionID = simulate.VersionID.String()
	simulateDTO.AccessToken = simulate.AccessToken

	for _, v := range simulate.Nodes {
		var term dtos.Node
		if v.Content != nil {
			term.Content = v.Content
		} else {
			term.Content = v.FunctionContent
		}
		term.ID = v.NodeID
		term.Name = v.Name
		term.Position = v.Position
		term.Type = v.Type

		simulateDTO.Nodes = append(simulateDTO.Nodes, term)
	}
	for _, v := range simulate.Edges {
		var term dtos.Edge
		term.ID = v.ID
		term.SourceHandle = v.SourceHandle
		term.SourceID = v.SourceID
		term.TargetID = v.TargetID
		term.Type = v.Type

		simulateDTO.Edges = append(simulateDTO.Edges, term)
	}
	return simulateDTO, err
}

func (r *repository) SimulateDetail(ctx *gin.Context, simulate_id string, t trace.Tracer) (dtos.SimulateResponseForDetail, error) {
	helpers.PushToTrace(t, ctx, "simulate.repository.SimulateDetail", "simulate.repository.SimulateDetail")

	var (
		simulate     entities.Simulate
		admin        entities.Admin
		organization entities.Organization
		resp         dtos.SimulateResponseForDetail
		simulate_log entities.SimulateLog
		result       struct {
			SuccessCount int64 `json:"success_count"`
			ErrorCount   int64 `json:"error_count"`
			HTTPCount    int64 `json:"http_count"`
			GRPCCount    int64 `json:"grpc_count"`
		}
	)

	if err := r.db.Where(query.WhereID, simulate_id).
		Where(query.WhereOrganizationID, state.CurrentAdminOrganization(ctx)).
		Preload(clause.Associations).
		First(&simulate).Error; err != nil {
		return resp, err
	}

	if err := r.db.Where(query.WhereID, simulate.OrganizationID).
		First(&organization).Error; err != nil {
		return resp, err
	}

	countMap := make(map[string]int)
	for _, v := range simulate.Nodes {
		countMap[v.Name]++
	}

	if err := r.db.Model(&entities.Admin{}).
		Where("id = ?", simulate.AdminID).
		First(&admin).Error; err != nil {
		return resp, err
	}

	r.db.Model(&entities.SimulateLog{}).
		Select(`
			SUM(CASE WHEN type = 'success' THEN 1 ELSE 0 END) AS success_count,
			SUM(CASE WHEN type = 'error' THEN 1 ELSE 0 END) AS error_count,
			SUM(CASE WHEN proto = 'grpc' THEN 1 ELSE 0 END) AS grpc_count,
			SUM(CASE WHEN proto = 'http' THEN 1 ELSE 0 END) AS http_count
			`).
		Where("simulate_id = ?", simulate.ID).
		Where("organization_id = ?", state.CurrentAdminOrganization(ctx)).
		Scan(&result)

	r.db.Model(&entities.SimulateLog{}).
		Where("simulate_id = ?", simulate.ID).
		Where("organization_id = ?", state.CurrentAdminOrganization(ctx)).
		Order("created_at desc").
		First(&simulate_log)

	resp.ID = simulate.ID
	resp.CreatedAt = simulate.CreatedAt.Format("2006-01-02 15:04:05")
	resp.ChangedBy = admin.Name
	resp.Name = simulate.Name
	resp.OrganizationID = organization.ID.String()
	resp.OrganizationName = organization.Name
	resp.AdminID = admin.ID.String()
	resp.AdminName = admin.Name
	resp.VersionNumber = simulate.VersionNumber
	resp.VersionName = simulate.VersionName
	resp.VersionID = simulate.VersionID.String()
	resp.AccessToken = simulate.AccessToken
	resp.LastWorkingTime = simulate_log.CreatedAt.Format("2006-01-02 15:04:05")
	resp.Charts.Error = int(result.ErrorCount)
	resp.Charts.Success = int(result.SuccessCount)
	resp.Charts.Http = int(result.HTTPCount)
	resp.Charts.Grpc = int(result.GRPCCount)

	return resp, nil
}

func (r *repository) AddDefaultRequest(ctx *gin.Context, t trace.Tracer, req dtos.RequestForDefaultRequest) error {
	var (
		new_default_request entities.DefaultRequest
	)

	if req.Type == 1 {
		r.db.WithContext(ctx).
			Where("type = ? AND organization_id = ?", 1, state.CurrentAdminOrganization(ctx)).
			Unscoped().
			Delete(&entities.DefaultRequest{})

		new_default_request.Value = req.Value
		new_default_request.SimulateID = uuid.Nil
		new_default_request.OrganizationID = state.CurrentAdminOrganization(ctx)
		new_default_request.AdminID = state.CurrentAdminUser(ctx)
		new_default_request.Type = req.Type
	} else {

		r.db.WithContext(ctx).
			Where("simulate_id = ? AND organization_id = ?", req.SimulateID, state.CurrentAdminOrganization(ctx)).
			Unscoped().
			Delete(&entities.DefaultRequest{})

		new_default_request.Value = req.Value
		new_default_request.SimulateID = uuid.MustParse(req.SimulateID)
		new_default_request.OrganizationID = state.CurrentAdminOrganization(ctx)
		new_default_request.AdminID = state.CurrentAdminUser(ctx)
		new_default_request.Type = req.Type

	}

	if err := r.db.WithContext(ctx).Create(&new_default_request).Error; err != nil {
		return err
	}

	return nil
}

func (r *repository) getDefaultRequest(ctx *gin.Context, t trace.Tracer, simulate_id, def_req_type string) (dtos.ResponseForDefaultRequest, error) {
	var (
		default_request      entities.DefaultRequest
		resp                 dtos.ResponseForDefaultRequest
		current_organization entities.Organization
		current_admin        entities.Admin
		current_simulate     entities.Simulate
	)
	base_query := r.db.WithContext(ctx).
		Where("organization_id = ?", state.CurrentAdminOrganization(ctx))

	if def_req_type == "2" {
		if err := base_query.Where("simulate_id = ? AND type = ?", simulate_id, "2").First(&default_request).Error; err != nil {
			return resp, err
		}
	} else {
		if err := base_query.Where("type = ?", 1).First(&default_request).Error; err != nil {
			return resp, err
		}
	}

	if err := r.db.Model(&entities.Organization{}).
		Where("id = ?", default_request.OrganizationID).First(&current_organization).Error; err != nil {
		return resp, err
	}

	if err := r.db.Model(&entities.Admin{}).
		Where("id = ?", default_request.AdminID).First(&current_admin).Error; err != nil {
		return resp, err
	}

	if def_req_type == "2" {
		if err := r.db.Model(&entities.Simulate{}).
			Where("id = ?", default_request.SimulateID).First(&current_simulate).Error; err != nil {
			return resp, err
		}
	}

	resp.ID = default_request.ID.String()
	resp.SimulateID = default_request.SimulateID.String()
	resp.OrganizationID = default_request.OrganizationID.String()
	resp.AdminID = default_request.AdminID.String()
	resp.OrganizationName = current_organization.Name
	resp.SimulateName = current_simulate.Name
	resp.AdminName = current_admin.Name
	resp.Value = default_request.Value
	resp.CreatedAt = default_request.CreatedAt.Format("2006-01-02 15:04:05")

	return resp, nil

}

func (r *repository) DeleteDefaultRequest(ctx *gin.Context, t trace.Tracer, id string) error {
	var default_request entities.DefaultRequest

	if err := r.db.WithContext(ctx).
		Where("simulate_id = ? AND organization_id = ?", id, state.CurrentAdminOrganization(ctx)).
		First(&default_request).Error; err != nil {
		return err
	}

	if err := r.db.WithContext(ctx).Unscoped().Delete(&default_request).Error; err != nil {
		return err
	}

	return nil
}
