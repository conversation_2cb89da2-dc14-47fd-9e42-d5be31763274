package nodes

import (
	"encoding/json"
	"fmt"

	"github.com/gorules/zen-go"
	"github.com/parsguru/pars-vue/pkg/helpers"
)

type excelNode struct {
}

type excelRequest struct {
	FileName        string          `json:"fileName"`
	ExcelColumns    []string        `json:"excelColumns"`
	SelectedColumns []string        `json:"selectedColumns"`
	ExcelData       [][]interface{} `json:"excelData"`
	FileType        string          `json:"fileType"`
	Operation       string          `json:"operation"`
	DataLimit       int             `json:"dataLimit"`
	SaveData        bool            `json:"saveData"`
	Input           interface{}     `json:"input"`
}

type excelResponse struct {
	ProcessedData   []map[string]interface{} `json:"processedData"`
	SelectedColumns []string                 `json:"selectedColumns"`
	RowCount        int                      `json:"rowCount"`
	Limit           int                      `json:"limit"`
	FileType        string                   `json:"fileType"`
	Operation       string                   `json:"operation"`
	Error           string                   `json:"error,omitempty"`
}

func (e excelNode) Handle(request zen.NodeRequest) (zen.NodeResponse, error) {
	var (
		output = make(map[string]any)
	)

	_, _, _, m, err := helpers.GetBaseIDsFromInput(request)
	if err != nil {
		output["error"] = "excel_error_1000: " + err.Error()
		return zen.NodeResponse{Output: output}, nil
	}

	output = m

	contentBytes, err := json.Marshal(request.Node.Config)
	if err != nil {
		return zen.NodeResponse{
			Output: excelResponse{
				Limit: 0,
				Error: fmt.Sprintf("Failed to parse node content: %v", err),
			},
		}, nil
	}

	var excel_config excelRequest
	if err := json.Unmarshal(contentBytes, &excel_config); err != nil {
		return zen.NodeResponse{
			Output: excelResponse{
				Limit: 0,
				Error: fmt.Sprintf("Failed to unmarshal excel config: %v", err),
			},
		}, nil
	}

	excel_config.Input = request.Input

	if excel_config.ExcelData == nil || len(excel_config.ExcelData) == 0 {
		return zen.NodeResponse{
			Output: excelResponse{
				Limit: excel_config.DataLimit,
				Error: "No Excel data found",
			},
		}, nil
	}

	if len(excel_config.SelectedColumns) == 0 {
		return zen.NodeResponse{
			Output: excelResponse{
				Limit: excel_config.DataLimit,
				Error: "No columns selected",
			},
		}, nil
	}

	processedData, err := processExcelData(excel_config)
	if err != nil {
		return zen.NodeResponse{
			Output: excelResponse{
				Limit: excel_config.DataLimit,
				Error: fmt.Sprintf("Excel processing failed: %v", err),
			},
		}, nil
	}

	output["excel"] = excelResponse{
		ProcessedData:   processedData,
		RowCount:        len(processedData),
		SelectedColumns: excel_config.SelectedColumns,
		Limit:           excel_config.DataLimit,
		FileType:        excel_config.FileType,
		Operation:       excel_config.Operation,
	}

	return zen.NodeResponse{
		Output: output,
	}, nil
}

func processExcelData(config excelRequest) ([]map[string]interface{}, error) {
	if len(config.ExcelData) < 2 {
		return nil, fmt.Errorf("insufficient data rows")
	}

	headers := make([]string, 0)
	if len(config.ExcelData) > 0 {
		for _, cell := range config.ExcelData[0] {
			if cell != nil {
				headers = append(headers, fmt.Sprintf("%v", cell))
			}
		}
	}

	columnIndexes := make(map[string]int)
	for _, selectedCol := range config.SelectedColumns {
		for i, header := range headers {
			if header == selectedCol {
				columnIndexes[selectedCol] = i
				break
			}
		}
	}

	var processedData []map[string]interface{}
	processedCount := 0

	dataLimit := config.DataLimit
	if dataLimit < 3 {
		dataLimit = 3
	}
	if dataLimit > 10000 {
		dataLimit = 10000
	}

	for i := 1; i < len(config.ExcelData) && processedCount < dataLimit; i++ {
		row := config.ExcelData[i]
		rowData := make(map[string]interface{})

		for _, selectedCol := range config.SelectedColumns {
			if colIndex, exists := columnIndexes[selectedCol]; exists {
				if colIndex < len(row) {
					rowData[selectedCol] = row[colIndex]
				} else {
					rowData[selectedCol] = nil
				}
			}
		}

		hasData := false
		for _, value := range rowData {
			if value != nil && fmt.Sprintf("%v", value) != "" {
				hasData = true
				break
			}
		}

		if hasData {
			processedData = append(processedData, rowData)
			processedCount++
		}
	}

	return processedData, nil
}
