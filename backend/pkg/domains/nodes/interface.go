package nodes

import (
	"errors"

	"github.com/gorules/zen-go"
)

type NodeHandler interface {
	Handle(request zen.NodeRequest) (zen.NodeResponse, error)
}

var customNodes = map[string]NodeHandler{
	"curlNode":    curlNode{},
	"dbNode":      dbNode{},
	"smsNode":     smsNode{},
	"mailNode":    mailNode{},
	"timeoutNode": timeoutNode{},
	"aiAgentNode": aiAgentNode{},
	"excelNode":   excelNode{},
}

func CustomNodeHandler(request zen.NodeRequest) (zen.NodeResponse, error) {
	nodeHandler, ok := customNodes[request.Node.Kind]
	if !ok {
		return zen.NodeResponse{}, errors.New("component not found")
	}

	return nodeHandler.Handle(request)
}
