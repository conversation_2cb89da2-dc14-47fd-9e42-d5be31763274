package nodes

import (
	"github.com/gorules/zen-go"
)

type aiAgentNode struct {
}

type aiAgentRequest struct {
	SelectedAgent string      `json:"selectedAgent"`
	Input         interface{} `json:"input"`
}

type aiAgentResponse struct {
	Output interface{} `json:"output"`
	Agent  string      `json:"agent"`
	Error  string      `json:"error,omitempty"`
}

func (a aiAgentNode) Handle(request zen.NodeRequest) (zen.NodeResponse, error) {
	return zen.NodeResponse{
		Output: aiAgentResponse{
			Output: nil,
			Agent:  "",
		},
	}, nil
}
