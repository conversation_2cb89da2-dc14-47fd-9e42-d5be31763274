package dash

import (
	"github.com/gin-gonic/gin"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/helpers"
	"go.opentelemetry.io/otel/trace"
)

type Service interface {
	Get(ctx *gin.Context, t trace.Tracer) (dtos.ResponseForDashboard, error)
	GetCurrentAdmin(ctx *gin.Context, t trace.Tracer) (dtos.ResponseForCurrentAdmin, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) Get(ctx *gin.Context, t trace.Tracer) (dtos.ResponseForDashboard, error) {
	helpers.PushToTrace(t, ctx, "dash.service.Get", "dash.service.Get")
	return s.repository.Get(ctx)
}

func (s *service) GetCurrentAdmin(ctx *gin.Context, t trace.Tracer) (dtos.ResponseForCurrentAdmin, error) {
	helpers.PushToTrace(t, ctx, "dash.service.GetCurrentAdmin", "dash.service.GetCurrentAdmin")
	return s.repository.GetCurrentAdmin(ctx)
}
