package dash

import (
	"context"

	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/state"
	"gorm.io/gorm"
)

type Repository interface {
	Get(ctx context.Context) (dtos.ResponseForDashboard, error)
	GetCurrentAdmin(ctx context.Context) (dtos.ResponseForCurrentAdmin, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) Get(ctx context.Context) (dtos.ResponseForDashboard, error) {
	var (
		simulates                                                                                        []entities.Simulate
		resp                                                                                             dtos.ResponseForDashboard
		total_evaluation_count, total_success_count, total_error_count, version_count, total_admin_count int64
		err                                                                                              error
	)

	if err = r.db.Model(&entities.Simulate{}).
		Where("organization_id = ?", state.CurrentAdminOrganization(ctx)).
		Order("created_at DESC").
		Find(&simulates).Error; err != nil {
		return resp, err
	}

	if err = r.db.Model(&entities.SimulateLog{}).
		Where("organization_id = ?", state.CurrentAdminOrganization(ctx)).
		Count(&total_evaluation_count).Error; err != nil {
		return resp, err
	}

	if err = r.db.Model(&entities.SimulateLog{}).
		Where("organization_id = ?", state.CurrentAdminOrganization(ctx)).
		Where("type = ?", "success").
		Count(&total_success_count).Error; err != nil {
		return resp, err
	}

	if err = r.db.Model(&entities.SimulateLog{}).
		Where("organization_id = ?", state.CurrentAdminOrganization(ctx)).
		Where("type = ?", "error").
		Count(&total_error_count).Error; err != nil {
		return resp, err
	}

	r.db.Model(&entities.Simulate{}).
		Where("organization_id = ?", state.CurrentAdminOrganization(ctx)).
		Group("version_id").
		Count(&version_count)

	r.db.Model(&entities.Admin{}).
		Where("organization_id = ?", state.CurrentAdminOrganization(ctx)).
		Count(&total_admin_count)

	resp.TotalSimulateCount = len(simulates)
	resp.TotalEvaluationCount = int(total_evaluation_count)
	resp.TotalSuccessCount = int(total_success_count)
	resp.TotalErrorCount = int(total_error_count)
	resp.TotalVersionCount = int(version_count)
	resp.TotalAdminCount = int(total_admin_count)

	if len(simulates) != 0 {
		resp.LastSimulate.ID = simulates[0].ID
		resp.LastSimulate.Name = simulates[0].Name
		resp.LastSimulate.Version = simulates[0].VersionName
		resp.LastSimulate.CreatedAt = simulates[0].CreatedAt.Format("2006-01-02 15:04:05")
	}

	return resp, nil
}

func (r *repository) GetCurrentAdmin(ctx context.Context) (dtos.ResponseForCurrentAdmin, error) {
	var (
		resp                 dtos.ResponseForCurrentAdmin
		current_admin        entities.Admin
		current_organization entities.Organization
	)

	if err := r.db.WithContext(ctx).
		Model(&entities.Admin{}).
		Where("id = ?", state.CurrentAdminUser(ctx)).
		First(&current_admin).Error; err != nil {
		return resp, err
	}

	if err := r.db.WithContext(ctx).
		Model(&entities.Organization{}).
		Where("id = ?", state.CurrentAdminOrganization(ctx)).
		First(&current_organization).Error; err != nil {
		return resp, err
	}

	resp.Name = current_admin.Name
	resp.Email = current_admin.Email
	resp.OrganizationName = current_organization.Name

	return resp, nil
}
