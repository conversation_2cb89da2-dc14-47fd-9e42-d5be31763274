package database

import (
	"errors"
	"fmt"
	"log"
	"os"

	"github.com/google/uuid"
	"github.com/parsguru/pars-vue/pkg/config"
	"github.com/parsguru/pars-vue/pkg/constants"
	"github.com/parsguru/pars-vue/pkg/constants/query"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/global"
	"github.com/parsguru/pars-vue/pkg/ptr"
	"github.com/parsguru/pars-vue/pkg/utils"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func InitSeed() {
	logsettings := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags), // io writer
		logger.Config{
			LogLevel: logger.Silent, // Log level
		},
	)
	db.Logger = logsettings

	var mainOrgCount int64
	db.Model(&entities.Organization{}).Where("main = ?", true).Count(&mainOrgCount)

	if mainOrgCount > 0 {
		log.Println("Seed data already exists.")
		var mainorg entities.Organization
		db.Model(&entities.Organization{}).
			Where("main = ?", true).
			First(&mainorg)

		global.SetMainOrgID(mainorg.ID)

		db.Logger = logger.Default
		return
	}

	var organization entities.Organization
	if err := db.Model(&entities.Organization{}).
		Where("main = ?", true).
		First(&organization).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			organization = entities.Organization{
				Name:           config.ReadValue().AppName,
				Main:           ptr.Bool(true),
				CheckoutDomain: config.ReadValue().Checkout.Url,
				DomainAddress:  utils.GetDomainAddressFromUrl(config.ReadValue().FrontBaseUrl),
			}
			db.Create(&organization)
		}
	}

	var role entities.Role
	if err := db.Model(&entities.Role{}).
		Where(query.BuildQuery(query.WhereOrganizationID), organization.ID).
		First(&role).Error; err != nil {
		role = entities.Role{
			Name:           "Admin",
			OrganizationID: organization.ID,
			Locked:         ptr.Bool(true),
		}

		db.Model(&entities.Role{}).Create(&role)

		for _, entity := range constants.EntityList {
			perm := entities.Permission{
				Entity: entity,
				RoleID: role.ID,
				Read:   true,
				Delete: true,
				Create: true,
				Update: true,
			}
			db.Model(&entities.Permission{}).Create(&perm)
		}
	} else {
		// we need to delete all permissions after create permissions
		db.Unscoped().Model(entities.Permission{}).
			Where("role_id = ?", role.ID).
			Delete(entities.Permission{})
		for _, entity := range constants.EntityList {
			perm := entities.Permission{
				Entity: entity,
				RoleID: role.ID,
				Read:   true,
				Delete: true,
				Create: true,
				Update: true,
			}
			db.Model(&entities.Permission{}).Create(&perm)
		}
	}

	var admin_id uuid.UUID
	if err := db.Where(query.BuildQuery(query.WhereOrganizationID), organization.ID).
		First(&entities.Admin{}).Error; err != nil {
		admin := entities.Admin{
			Email:          config.ReadValue().AdminEmail,
			Name:           config.ReadValue().AdminName,
			Password:       utils.Bcrypt(config.ReadValue().AdminPassword),
			RoleID:         role.ID,
			OrganizationID: organization.ID,
		}
		db.Create(&admin)
		admin_id = admin.ID
	}

	var default_request entities.DefaultRequest
	if err := db.Model(&entities.DefaultRequest{}).
		Where("type=2").
		First(&default_request).Error; err != nil && err == gorm.ErrRecordNotFound {

		default_request.OrganizationID = organization.ID
		default_request.SimulateID = uuid.Nil
		default_request.AdminID = admin_id
		default_request.Type = 1
		default_request.Value = fmt.Sprintf(`{"name": "%s","value": "pars"}`, organization.Name)
		db.Create(&default_request)
	}

	db.Logger = logger.Default
}
