name: Release Binaries

on:
  release:
    types: [published]

jobs:
  push_to_registry:
    name: Build Binary
    runs-on: ubuntu-latest
    steps:
      - name: Check out the repo
        uses: actions/checkout@v5
      
      - uses: actions/setup-node@v5
        with:
          node-version: 22

      - name: Install Go
        uses: actions/setup-go@v6
        with:
          go-version: "^1.25"

      - name: Fronted Version Set
        run: node ./version.js ${{ github.ref_name }}
        working-directory: ./frontend

      - name: Frontend Embeding
        run: make prepare
        working-directory: ./backend

      - name: Backend Dependecy Install
        run: go mod download
        working-directory: ./backend

      - name: Log in to Tapsilat Registry
        uses: docker/login-action@v3
        with:
          registry: registry.tapsilat.dev
          username: ${{ secrets.REGISTRY_USER }}
          password: ${{ secrets.REGISTRY_PASS }}

      - name: Push to Tapsilat Registry
        uses: docker/build-push-action@v6
        with:
          context: ./backend
          file: ./backend/Dockerfile
          push: true
          build-args: |
            VERSION=${{github.ref_name }}
          tags: |
            registry.tapsilat.dev/pars/pars-vue:${{ github.ref_name }}
